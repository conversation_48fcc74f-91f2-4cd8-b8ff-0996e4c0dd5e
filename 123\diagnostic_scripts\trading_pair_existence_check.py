#!/usr/bin/env python3
"""
交易对存在性检查脚本
验证ICNT-USDT和SPK-USDT在各个交易所的存在性
"""

import os
import sys
import asyncio
import time
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class TradingPairExistenceCheck:
    def __init__(self):
        self.results = {
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "pair_existence": {},
            "root_cause_identified": False,
            "fix_recommendations": []
        }
        
    def print_header(self, title: str):
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
        
    def print_result(self, test_name: str, passed: bool, details: str = ""):
        status = "✅ EXISTS" if passed else "❌ NOT_EXISTS"
        print(f"{status} {test_name}: {details}")
        
    async def check_bybit_pair_existence(self):
        """检查Bybit交易对存在性"""
        self.print_header("Bybit交易对存在性检查")
        
        try:
            from exchanges.bybit_exchange import BybitExchange
            
            # 创建Bybit实例
            bybit = BybitExchange(
                api_key="dummy",
                api_secret="dummy", 
                testnet=False
            )
            
            # 测试交易对
            test_cases = [
                ("ICNT-USDT", "spot"),
                ("ICNT-USDT", "futures"),
                ("SPK-USDT", "spot"),
                ("SPK-USDT", "futures")
            ]
            
            for symbol, market_type in test_cases:
                case_key = f"{symbol}_{market_type}"
                print(f"\n🧪 检查 Bybit {symbol} {market_type}...")
                
                try:
                    category = "spot" if market_type == "spot" else "linear"
                    bybit_symbol = symbol.replace('-', '')
                    
                    response = await bybit.get_instruments_info(category, bybit_symbol)
                    
                    if response and "result" in response and "list" in response["result"]:
                        instruments = response["result"]["list"]
                        exists = len(instruments) > 0
                        
                        if exists:
                            instrument = instruments[0]
                            lot_size_filter = instrument.get("lotSizeFilter", {})
                            
                            # 获取真实的步长信息
                            if market_type == "spot":
                                step_size = lot_size_filter.get("basePrecision", "unknown")
                            else:
                                step_size = lot_size_filter.get("qtyStep", "unknown")
                                
                            self.results["pair_existence"][case_key] = {
                                "exists": True,
                                "step_size": step_size,
                                "min_qty": lot_size_filter.get("minOrderQty", "unknown"),
                                "max_qty": lot_size_filter.get("maxOrderQty", "unknown"),
                                "raw_data": lot_size_filter
                            }
                            
                            self.print_result(
                                f"Bybit {case_key}", 
                                True, 
                                f"步长={step_size}, 最小数量={lot_size_filter.get('minOrderQty', 'unknown')}"
                            )
                        else:
                            self.results["pair_existence"][case_key] = {
                                "exists": False,
                                "error": "交易对不存在"
                            }
                            self.print_result(f"Bybit {case_key}", False, "交易对不存在")
                    else:
                        self.results["pair_existence"][case_key] = {
                            "exists": False,
                            "error": "API响应为空"
                        }
                        self.print_result(f"Bybit {case_key}", False, "API响应为空")
                        
                except Exception as e:
                    error_str = str(e)
                    
                    # 分析错误类型
                    if "403" in error_str:
                        # 403错误可能是因为没有API密钥，但不代表交易对不存在
                        self.results["pair_existence"][case_key] = {
                            "exists": "unknown",
                            "error": "API访问受限(403)"
                        }
                        self.print_result(f"Bybit {case_key}", False, "API访问受限(403) - 需要真实API密钥")
                    elif "10001" in error_str and "symbol invalid" in error_str:
                        # 明确的交易对不存在错误
                        self.results["pair_existence"][case_key] = {
                            "exists": False,
                            "error": "交易对无效(10001)"
                        }
                        self.print_result(f"Bybit {case_key}", False, "交易对无效(10001)")
                    else:
                        self.results["pair_existence"][case_key] = {
                            "exists": "unknown",
                            "error": error_str
                        }
                        self.print_result(f"Bybit {case_key}", False, f"错误: {error_str}")
                        
        except Exception as e:
            self.print_result("Bybit交易所初始化", False, f"错误: {e}")
            
    def analyze_root_cause(self):
        """分析根本原因"""
        self.print_header("根本原因分析")
        
        # 分析ICNT-USDT期货问题
        icnt_futures = self.results["pair_existence"].get("ICNT-USDT_futures", {})
        icnt_spot = self.results["pair_existence"].get("ICNT-USDT_spot", {})
        spk_spot = self.results["pair_existence"].get("SPK-USDT_spot", {})
        
        print("📊 交易对存在性分析:")
        print(f"   ICNT-USDT现货: {icnt_spot.get('exists', 'unknown')}")
        print(f"   ICNT-USDT期货: {icnt_futures.get('exists', 'unknown')}")
        print(f"   SPK-USDT现货: {spk_spot.get('exists', 'unknown')}")
        
        # 根本原因1: ICNT-USDT期货不存在
        if icnt_futures.get("exists") == False:
            print("\n🚨 根本原因1: ICNT-USDT期货合约在Bybit上不存在！")
            print("   这解释了为什么会出现 '10001: Qty invalid' 错误")
            print("   系统试图在不存在的期货合约上下单")
            self.results["root_cause_identified"] = True
            
            self.results["fix_recommendations"].append({
                "issue": "ICNT-USDT期货不存在",
                "root_cause": "Bybit没有ICNT-USDT期货合约",
                "impact": "系统试图在不存在的合约上下单导致API错误",
                "fix": "从期货交易列表中移除ICNT-USDT，或者检查其他交易所是否支持",
                "priority": "CRITICAL"
            })
        
        # 根本原因2: SPK-USDT精度问题
        if spk_spot.get("exists") == True:
            step_size = spk_spot.get("step_size", "unknown")
            if step_size != "unknown" and step_size != "0.001":
                print(f"\n🚨 根本原因2: SPK-USDT现货真实步长是{step_size}，不是系统默认的0.001")
                print("   这解释了为什么会出现 '170137: Order quantity has too many decimals' 错误")
                
                self.results["fix_recommendations"].append({
                    "issue": "SPK-USDT现货精度不匹配",
                    "root_cause": f"真实步长是{step_size}，系统使用默认值0.001",
                    "impact": "数量321.995不符合真实精度要求",
                    "fix": f"更新交易规则预加载器，使用真实步长{step_size}",
                    "priority": "HIGH"
                })
        
        # 通用问题：交易规则获取失败
        if not self.results["root_cause_identified"]:
            print("\n⚠️ 无法完全确定根本原因，可能是API访问限制")
            self.results["fix_recommendations"].append({
                "issue": "无法获取真实交易规则",
                "root_cause": "API访问受限或交易对不存在",
                "impact": "系统使用不准确的默认值",
                "fix": "配置真实API密钥或手动配置交易规则",
                "priority": "MEDIUM"
            })
            
    def generate_fix_plan(self):
        """生成修复计划"""
        self.print_header("修复计划")
        
        print("🔧 基于分析结果的修复计划:")
        
        for i, rec in enumerate(self.results["fix_recommendations"], 1):
            print(f"\n{i}. {rec['issue']} ({rec['priority']})")
            print(f"   根本原因: {rec['root_cause']}")
            print(f"   影响: {rec['impact']}")
            print(f"   修复方案: {rec['fix']}")
            
        # 具体的修复步骤
        print(f"\n{'='*40}")
        print("🎯 具体修复步骤:")
        print("1. 立即修复: 从系统中移除不存在的ICNT-USDT期货交易对")
        print("2. 精度修复: 获取SPK-USDT的真实交易规则并更新系统配置")
        print("3. 系统改进: 添加交易对存在性验证，避免在不存在的合约上下单")
        print("4. 监控改进: 添加API错误码的智能识别，区分精度错误和交易对不存在错误")
        
    async def run_check(self):
        """运行完整检查"""
        print("🚀 开始交易对存在性检查...")
        
        await self.check_bybit_pair_existence()
        self.analyze_root_cause()
        self.generate_fix_plan()
        
        # 保存结果
        results_file = "123/diagnostic_results/trading_pair_existence_check.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            
        print(f"\n📊 检查结果已保存到: {results_file}")
        
        # 总结
        print(f"\n{'='*60}")
        print("🎯 检查总结")
        print(f"{'='*60}")
        print(f"🔍 交易对检查: {len(self.results['pair_existence'])}")
        print(f"🚨 根本原因识别: {'是' if self.results['root_cause_identified'] else '否'}")
        print(f"🔧 修复建议: {len(self.results['fix_recommendations'])}")
        
        if self.results["root_cause_identified"]:
            print("✅ 已识别根本原因，可以开始修复")
        else:
            print("⚠️ 需要进一步调查")

if __name__ == "__main__":
    check = TradingPairExistenceCheck()
    asyncio.run(check.run_check())
