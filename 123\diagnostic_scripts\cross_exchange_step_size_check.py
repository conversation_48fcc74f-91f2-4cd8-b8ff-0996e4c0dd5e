#!/usr/bin/env python3
"""
横向检查所有交易所的步长问题
检查Gate.io、OKX、Bybit是否都存在步长错误
"""

import os
import sys
import asyncio

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def check_all_exchanges_step_sizes():
    print("🔍 横向检查所有交易所的步长问题...")
    print("="*80)
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        from exchanges.gate_exchange import GateExchange
        from exchanges.okx_exchange import OKXExchange
        from exchanges.bybit_exchange import BybitExchange
        
        preloader = get_trading_rules_preloader()
        
        # 清除缓存确保获取最新规则
        preloader.trading_rules.clear()
        preloader.unsupported_pairs.clear()
        
        # 测试交易对
        test_pairs = [
            ("ICNT-USDT", "futures"),
            ("SPK-USDT", "spot"),
            ("SOL-USDT", "futures"),
            ("SOL-USDT", "spot")
        ]
        
        exchanges = ["gate", "okx", "bybit"]
        
        print(f"📊 测试交易对: {len(test_pairs)}个")
        print(f"📊 测试交易所: {exchanges}")
        print()
        
        results = {}
        
        for exchange_name in exchanges:
            print(f"🏢 检查 {exchange_name.upper()} 交易所...")
            results[exchange_name] = {}
            
            for symbol, market_type in test_pairs:
                print(f"   🧪 {symbol} {market_type}...")
                
                try:
                    # 获取交易规则
                    rule = preloader.get_trading_rule(exchange_name, symbol, market_type)
                    
                    if rule:
                        step_size = float(rule.qty_step)
                        source = rule.source
                        
                        results[exchange_name][f"{symbol}_{market_type}"] = {
                            "step_size": step_size,
                            "source": source,
                            "min_qty": float(rule.min_qty),
                            "status": "success"
                        }
                        
                        # 检查是否使用默认值
                        if source == "default":
                            print(f"      ⚠️  步长: {step_size} (来源: {source}) - 可能有问题!")
                        elif source in ["api", "api_verified", "api_fallback"]:
                            print(f"      ✅ 步长: {step_size} (来源: {source})")
                        else:
                            print(f"      ❓ 步长: {step_size} (来源: {source})")
                            
                    else:
                        results[exchange_name][f"{symbol}_{market_type}"] = {
                            "status": "failed",
                            "error": "无法获取规则"
                        }
                        print(f"      ❌ 无法获取交易规则")
                        
                except Exception as e:
                    results[exchange_name][f"{symbol}_{market_type}"] = {
                        "status": "error",
                        "error": str(e)
                    }
                    print(f"      💥 异常: {e}")
                    
            print()
            
        # 分析结果
        print("="*80)
        print("🎯 横向分析结果")
        print("="*80)
        
        # 检查默认值使用情况
        default_usage = {}
        api_usage = {}
        
        for exchange_name in exchanges:
            default_count = 0
            api_count = 0
            total_count = 0
            
            for pair_key, data in results[exchange_name].items():
                if data.get("status") == "success":
                    total_count += 1
                    source = data.get("source", "unknown")
                    
                    if source == "default":
                        default_count += 1
                    elif source in ["api", "api_verified", "api_fallback"]:
                        api_count += 1
                        
            default_usage[exchange_name] = {
                "default": default_count,
                "api": api_count,
                "total": total_count,
                "default_ratio": default_count / total_count if total_count > 0 else 0
            }
            
        # 输出分析
        for exchange_name in exchanges:
            stats = default_usage[exchange_name]
            ratio = stats["default_ratio"]
            
            print(f"📊 {exchange_name.upper()}:")
            print(f"   API规则: {stats['api']}/{stats['total']} ({(1-ratio)*100:.1f}%)")
            print(f"   默认规则: {stats['default']}/{stats['total']} ({ratio*100:.1f}%)")
            
            if ratio > 0.5:
                print(f"   🚨 警告: {exchange_name}大量使用默认规则，可能存在API调用问题!")
            elif ratio > 0:
                print(f"   ⚠️  注意: {exchange_name}部分使用默认规则")
            else:
                print(f"   ✅ {exchange_name}完全使用API规则")
            print()
            
        # 检查步长一致性
        print("🔍 步长一致性检查:")
        print("-" * 40)
        
        for symbol, market_type in test_pairs:
            pair_key = f"{symbol}_{market_type}"
            print(f"📈 {symbol} {market_type}:")
            
            step_sizes = {}
            for exchange_name in exchanges:
                data = results[exchange_name].get(pair_key, {})
                if data.get("status") == "success":
                    step_size = data["step_size"]
                    source = data["source"]
                    step_sizes[exchange_name] = (step_size, source)
                    print(f"   {exchange_name}: {step_size} ({source})")
                else:
                    print(f"   {exchange_name}: 失败")
                    
            # 检查是否有显著差异
            if len(step_sizes) > 1:
                values = [v[0] for v in step_sizes.values()]
                min_val, max_val = min(values), max(values)
                
                if max_val / min_val > 10:  # 差异超过10倍
                    print(f"   🚨 步长差异巨大: {min_val} ~ {max_val}")
                elif max_val != min_val:
                    print(f"   ⚠️  步长有差异: {min_val} ~ {max_val}")
                else:
                    print(f"   ✅ 步长一致: {min_val}")
            print()
            
        # 总结
        print("="*80)
        print("🎯 总结")
        print("="*80)
        
        problem_exchanges = []
        for exchange_name in exchanges:
            ratio = default_usage[exchange_name]["default_ratio"]
            if ratio > 0.3:  # 超过30%使用默认规则
                problem_exchanges.append(exchange_name)
                
        if problem_exchanges:
            print(f"🚨 发现问题交易所: {', '.join(problem_exchanges)}")
            print(f"   这些交易所大量使用默认规则，可能存在API调用问题")
        else:
            print(f"✅ 所有交易所都正常使用API规则")
            
        print(f"\n💡 建议:")
        print(f"   1. 重点检查使用默认规则的交易对")
        print(f"   2. 验证API调用是否正常工作")
        print(f"   3. 确保步长规则符合交易所实际要求")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_all_exchanges_step_sizes())
