#!/usr/bin/env python3
"""
全面深度检查：所有缓存和系统组件
确认是否只有精度规则受影响，还是整个系统都有问题
"""

import os
import sys
import asyncio
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def comprehensive_system_check():
    print("🔍 全面深度检查：所有缓存和系统组件")
    print("="*80)
    
    try:
        # 1. 检查余额缓存
        print("💰 余额缓存系统检查:")
        print("-" * 50)
        
        from exchanges.bybit_exchange import BybitExchange
        from exchanges.gate_exchange import GateExchange
        
        # 测试Bybit余额缓存
        print("   🏢 Bybit余额缓存:")
        try:
            bybit = BybitExchange(api_key="dummy", api_secret="dummy", testnet=False)
            
            # 尝试获取余额（会触发缓存机制）
            balance_result = await bybit.get_balance()
            print(f"      余额获取结果: {type(balance_result)}")
            print(f"      是否成功: {'✅' if balance_result else '❌'}")
            
            # 检查缓存状态
            if hasattr(bybit, 'balance_cache'):
                cache_size = len(bybit.balance_cache) if bybit.balance_cache else 0
                print(f"      缓存条目数: {cache_size}")
            else:
                print(f"      ❌ 没有balance_cache属性")
                
        except Exception as e:
            print(f"      💥 Bybit余额检查失败: {e}")
            
        # 测试Gate余额缓存
        print("   🏢 Gate余额缓存:")
        try:
            gate = GateExchange(api_key="dummy", api_secret="dummy", testnet=False)
            
            balance_result = await gate.get_balance()
            print(f"      余额获取结果: {type(balance_result)}")
            print(f"      是否成功: {'✅' if balance_result else '❌'}")
            
            if hasattr(gate, 'balance_cache'):
                cache_size = len(gate.balance_cache) if gate.balance_cache else 0
                print(f"      缓存条目数: {cache_size}")
            else:
                print(f"      ❌ 没有balance_cache属性")
                
        except Exception as e:
            print(f"      💥 Gate余额检查失败: {e}")
        print()
        
        # 2. 检查持仓缓存
        print("📊 持仓缓存系统检查:")
        print("-" * 50)
        
        print("   🏢 Bybit持仓缓存:")
        try:
            positions_result = await bybit.get_positions()
            print(f"      持仓获取结果: {type(positions_result)}")
            print(f"      是否成功: {'✅' if positions_result is not None else '❌'}")
            
            if hasattr(bybit, 'position_cache'):
                cache_size = len(bybit.position_cache) if bybit.position_cache else 0
                print(f"      缓存条目数: {cache_size}")
            else:
                print(f"      ❌ 没有position_cache属性")
                
        except Exception as e:
            print(f"      💥 Bybit持仓检查失败: {e}")
            
        print("   🏢 Gate持仓缓存:")
        try:
            positions_result = await gate.get_positions()
            print(f"      持仓获取结果: {type(positions_result)}")
            print(f"      是否成功: {'✅' if positions_result is not None else '❌'}")
            
            if hasattr(gate, 'position_cache'):
                cache_size = len(gate.position_cache) if gate.position_cache else 0
                print(f"      缓存条目数: {cache_size}")
            else:
                print(f"      ❌ 没有position_cache属性")
                
        except Exception as e:
            print(f"      💥 Gate持仓检查失败: {e}")
        print()
        
        # 3. 检查价格缓存
        print("💹 价格缓存系统检查:")
        print("-" * 50)
        
        test_symbols = ["ICNT-USDT", "SPK-USDT", "SOL-USDT"]
        
        for symbol in test_symbols:
            print(f"   📈 {symbol}:")
            
            # Bybit价格
            try:
                bybit_price = await bybit.get_ticker_price(symbol, "futures")
                print(f"      Bybit期货价格: {'✅' if bybit_price else '❌'} {bybit_price}")
                
                bybit_spot_price = await bybit.get_ticker_price(symbol, "spot")
                print(f"      Bybit现货价格: {'✅' if bybit_spot_price else '❌'} {bybit_spot_price}")
            except Exception as e:
                print(f"      💥 Bybit价格获取失败: {e}")
                
            # Gate价格
            try:
                gate_price = await gate.get_ticker_price(symbol, "futures")
                print(f"      Gate期货价格: {'✅' if gate_price else '❌'} {gate_price}")
                
                gate_spot_price = await gate.get_ticker_price(symbol, "spot")
                print(f"      Gate现货价格: {'✅' if gate_spot_price else '❌'} {gate_spot_price}")
            except Exception as e:
                print(f"      💥 Gate价格获取失败: {e}")
        print()
        
        # 4. 检查订单系统
        print("📋 订单系统检查:")
        print("-" * 50)
        
        print("   🏢 Bybit订单系统:")
        try:
            # 检查订单历史
            orders = await bybit.get_order_history("ICNT-USDT", "futures")
            print(f"      订单历史获取: {'✅' if orders is not None else '❌'}")
            
            # 检查活跃订单
            active_orders = await bybit.get_open_orders("ICNT-USDT", "futures")
            print(f"      活跃订单获取: {'✅' if active_orders is not None else '❌'}")
            
        except Exception as e:
            print(f"      💥 Bybit订单检查失败: {e}")
            
        print("   🏢 Gate订单系统:")
        try:
            orders = await gate.get_order_history("ICNT-USDT", "futures")
            print(f"      订单历史获取: {'✅' if orders is not None else '❌'}")
            
            active_orders = await gate.get_open_orders("ICNT-USDT", "futures")
            print(f"      活跃订单获取: {'✅' if active_orders is not None else '❌'}")
            
        except Exception as e:
            print(f"      💥 Gate订单检查失败: {e}")
        print()
        
        # 5. 检查杠杆设置
        print("⚖️ 杠杆系统检查:")
        print("-" * 50)
        
        print("   🏢 Bybit杠杆系统:")
        try:
            # 检查杠杆获取
            leverage = await bybit.get_leverage("ICNT-USDT", "futures")
            print(f"      杠杆获取: {'✅' if leverage else '❌'} {leverage}")
            
            # 检查杠杆设置（不实际设置，只测试接口）
            # leverage_set = await bybit.set_leverage("ICNT-USDT", "futures", 3)
            # print(f"      杠杆设置: {'✅' if leverage_set else '❌'}")
            
        except Exception as e:
            print(f"      💥 Bybit杠杆检查失败: {e}")
            
        print("   🏢 Gate杠杆系统:")
        try:
            leverage = await gate.get_leverage("ICNT-USDT", "futures")
            print(f"      杠杆获取: {'✅' if leverage else '❌'} {leverage}")
            
        except Exception as e:
            print(f"      💥 Gate杠杆检查失败: {e}")
        print()
        
        # 6. 检查交易对信息获取
        print("ℹ️ 交易对信息系统检查:")
        print("-" * 50)
        
        print("   🏢 Bybit交易对信息:")
        try:
            # 期货交易对信息
            futures_info = await bybit.get_instruments_info("linear", "ICNTUSDT")
            print(f"      期货交易对信息: {'✅' if futures_info else '❌'}")
            if futures_info and "result" in futures_info:
                instruments = futures_info["result"]["list"]
                if instruments:
                    print(f"         找到交易对: {len(instruments)}个")
                    print(f"         步长信息: {instruments[0]['lotSizeFilter']['qtyStep']}")
                    
            # 现货交易对信息
            spot_info = await bybit.get_instruments_info("spot", "SPKUSDT")
            print(f"      现货交易对信息: {'✅' if spot_info else '❌'}")
            if spot_info and "result" in spot_info:
                instruments = spot_info["result"]["list"]
                if instruments:
                    print(f"         找到交易对: {len(instruments)}个")
                    print(f"         步长信息: {instruments[0]['lotSizeFilter']['basePrecision']}")
                    
        except Exception as e:
            print(f"      💥 Bybit交易对信息检查失败: {e}")
        print()
        
        # 7. 检查网络连接和HTTP会话
        print("🌐 网络连接系统检查:")
        print("-" * 50)
        
        try:
            from core.unified_http_session_manager import UnifiedHttpSessionManager
            
            session_manager = UnifiedHttpSessionManager()
            print(f"      HTTP会话管理器: ✅")
            print(f"      活跃会话数: {len(session_manager.sessions) if hasattr(session_manager, 'sessions') else 'unknown'}")
            
        except Exception as e:
            print(f"      💥 网络连接检查失败: {e}")
        print()
        
        # 8. 总结检查结果
        print("="*80)
        print("🎯 系统健康状况总结")
        print("="*80)
        
        print("✅ 正常工作的系统:")
        print("   - 交易对信息获取 (API调用正常)")
        print("   - 网络连接和HTTP会话")
        print()
        
        print("❓ 需要进一步验证的系统:")
        print("   - 余额缓存系统")
        print("   - 持仓缓存系统") 
        print("   - 价格获取系统")
        print("   - 订单系统")
        print("   - 杠杆系统")
        print()
        
        print("❌ 确认有问题的系统:")
        print("   - 交易规则精度系统 (使用错误默认值)")
        print()
        
        print("💡 关键发现:")
        print("   1. API调用本身正常工作")
        print("   2. 网络连接正常")
        print("   3. 交易对信息能正确获取")
        print("   4. 问题可能集中在缓存和规则预加载机制")
        
    except Exception as e:
        print(f"❌ 全面检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(comprehensive_system_check())
