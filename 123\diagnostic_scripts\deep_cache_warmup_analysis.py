#!/usr/bin/env python3
"""
深度检查：预热机制和缓存系统影响分析
1. 检查预热机制是否导致事件循环问题
2. 检查其他缓存系统是否受影响
3. 分析问题是否只限于精度规则
"""

import os
import sys
import asyncio
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def analyze_warmup_and_cache_systems():
    print("🔍 深度检查：预热机制和缓存系统影响分析")
    print("="*80)
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        
        preloader = get_trading_rules_preloader()
        
        print("📊 系统状态检查:")
        print(f"   预加载完成状态: {preloader.preload_completed}")
        print(f"   正在预加载: {preloader.is_preloading}")
        print(f"   交易规则缓存数量: {len(preloader.trading_rules)}")
        print(f"   不支持交易对数量: {len(preloader.unsupported_pairs)}")
        print()
        
        # 检查预热相关方法
        print("🔥 预热机制检查:")
        print("-" * 40)
        
        warmup_methods = [
            'warmup_all_caches',
            'warmup_trading_rules_cache', 
            'warmup_balance_cache',
            'warmup_leverage_cache'
        ]
        
        for method_name in warmup_methods:
            has_method = hasattr(preloader, method_name)
            print(f"   {method_name}: {'✅ 存在' if has_method else '❌ 不存在'}")
            
            if has_method:
                method = getattr(preloader, method_name)
                is_async = asyncio.iscoroutinefunction(method)
                print(f"      类型: {'异步方法' if is_async else '同步方法'}")
        print()
        
        # 检查事件循环状态
        print("🔄 事件循环状态检查:")
        print("-" * 40)
        
        try:
            loop = asyncio.get_running_loop()
            print(f"   当前事件循环: {loop}")
            print(f"   事件循环运行中: True")
            print(f"   事件循环关闭: {loop.is_closed()}")
        except RuntimeError as e:
            print(f"   事件循环状态: {e}")
        print()
        
        # 检查其他缓存系统
        print("💾 其他缓存系统检查:")
        print("-" * 40)
        
        # 检查余额缓存
        try:
            from exchanges.bybit_exchange import BybitExchange
            bybit = BybitExchange(api_key="dummy", api_secret="dummy", testnet=False)
            
            print("   Bybit交易所缓存:")
            if hasattr(bybit, 'balance_cache'):
                print(f"      余额缓存: {len(bybit.balance_cache) if bybit.balance_cache else 0}条")
            if hasattr(bybit, 'position_cache'):
                print(f"      持仓缓存: {len(bybit.position_cache) if bybit.position_cache else 0}条")
            if hasattr(bybit, 'leverage_cache'):
                print(f"      杠杆缓存: {len(bybit.leverage_cache) if bybit.leverage_cache else 0}条")
                
        except Exception as e:
            print(f"   Bybit缓存检查失败: {e}")
            
        # 检查Gate缓存
        try:
            from exchanges.gate_exchange import GateExchange
            gate = GateExchange(api_key="dummy", api_secret="dummy", testnet=False)
            
            print("   Gate交易所缓存:")
            if hasattr(gate, 'balance_cache'):
                print(f"      余额缓存: {len(gate.balance_cache) if gate.balance_cache else 0}条")
            if hasattr(gate, 'position_cache'):
                print(f"      持仓缓存: {len(gate.position_cache) if gate.position_cache else 0}条")
                
        except Exception as e:
            print(f"   Gate缓存检查失败: {e}")
            
        # 检查OKX缓存
        try:
            from exchanges.okx_exchange import OKXExchange
            okx = OKXExchange(api_key="dummy", api_secret="dummy", testnet=False)
            
            print("   OKX交易所缓存:")
            if hasattr(okx, 'balance_cache'):
                print(f"      余额缓存: {len(okx.balance_cache) if okx.balance_cache else 0}条")
            if hasattr(okx, 'position_cache'):
                print(f"      持仓缓存: {len(okx.position_cache) if okx.position_cache else 0}条")
                
        except Exception as e:
            print(f"   OKX缓存检查失败: {e}")
        print()
        
        # 测试单独的API调用（不通过预加载器）
        print("🧪 直接API调用测试（绕过预加载器）:")
        print("-" * 40)
        
        async def test_direct_api():
            try:
                from exchanges.bybit_exchange import BybitExchange
                bybit = BybitExchange(api_key="dummy", api_secret="dummy", testnet=False)
                
                print("   测试Bybit直接API调用...")
                
                # 测试获取交易对信息
                result = await bybit.get_instruments_info("linear", "ICNTUSDT")
                if result and "result" in result:
                    print("   ✅ Bybit API调用成功")
                    instruments = result["result"]["list"]
                    if instruments:
                        instrument = instruments[0]
                        step_size = instrument["lotSizeFilter"]["qtyStep"]
                        print(f"      ICNT期货步长: {step_size}")
                else:
                    print("   ❌ Bybit API调用失败")
                    
            except Exception as e:
                print(f"   💥 直接API调用异常: {e}")
                
        # 在新的事件循环中测试
        try:
            # 创建新的事件循环来测试
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            new_loop.run_until_complete(test_direct_api())
            new_loop.close()
            print("   ✅ 新事件循环测试成功")
        except Exception as e:
            print(f"   ❌ 新事件循环测试失败: {e}")
        print()
        
        # 检查预加载器初始化时机
        print("⏰ 预加载器初始化时机分析:")
        print("-" * 40)
        
        # 检查是否在模块导入时就开始预热
        import importlib
        import sys
        
        # 检查已导入的模块
        loaded_modules = [name for name in sys.modules.keys() if 'trading_rules' in name or 'preloader' in name]
        print(f"   已加载的预加载器相关模块: {loaded_modules}")
        
        # 检查预加载器的创建时间戳
        if hasattr(preloader, 'init_timestamp'):
            init_time = preloader.init_timestamp
            current_time = time.time()
            elapsed = current_time - init_time
            print(f"   预加载器创建时间: {elapsed:.2f}秒前")
        print()
        
        # 分析问题范围
        print("🎯 问题范围分析:")
        print("-" * 40)
        
        print("   1. 精度规则问题:")
        print("      - 所有交易所都无法获取API规则")
        print("      - 异步调用失败：事件循环冲突")
        print("      - 回退机制失败：变量未定义")
        print()
        
        print("   2. 其他缓存系统:")
        print("      - 余额缓存：需要测试")
        print("      - 持仓缓存：需要测试") 
        print("      - 杠杆缓存：需要测试")
        print()
        
        print("   3. 可能的根本原因:")
        print("      - 预热机制在系统启动时创建了事件循环")
        print("      - 后续的API调用无法在已有事件循环中使用asyncio.run()")
        print("      - 这是Python异步编程的常见陷阱")
        print()
        
        # 建议的解决方案
        print("💡 可能的解决方案:")
        print("-" * 40)
        print("   1. 修改异步调用逻辑：")
        print("      - 使用await而不是asyncio.run()")
        print("      - 或者使用loop.create_task()")
        print()
        print("   2. 分离预热和运行时调用：")
        print("      - 预热阶段使用异步")
        print("      - 运行时使用同步API")
        print()
        print("   3. 修复回退机制：")
        print("      - 确保变量正确定义")
        print("      - 提供正确的默认值")
        
    except Exception as e:
        print(f"❌ 深度检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_warmup_and_cache_systems()
