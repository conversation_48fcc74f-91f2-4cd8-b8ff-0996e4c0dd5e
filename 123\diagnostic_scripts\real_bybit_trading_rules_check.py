#!/usr/bin/env python3
"""
真实Bybit交易规则检查脚本
直接从Bybit API获取ICNT-USDT和SPK-USDT的真实交易规则
"""

import os
import sys
import asyncio
import time
import json
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class RealBybitTradingRulesCheck:
    def __init__(self):
        self.results = {
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "real_trading_rules": {},
            "precision_comparison": {},
            "root_cause_analysis": {}
        }
        
    def print_header(self, title: str):
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
        
    def print_result(self, test_name: str, passed: bool, details: str = ""):
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}: {details}")

    def _calculate_precision(self, step_str: str) -> int:
        """计算精度位数"""
        try:
            if '.' in step_str:
                return len(step_str.split('.')[1])
            else:
                return 0
        except:
            return 3  # 默认精度
        
    async def get_real_bybit_trading_rules(self):
        """获取真实的Bybit交易规则"""
        self.print_header("获取真实Bybit交易规则")
        
        try:
            # 初始化Bybit交易所
            from exchanges.bybit_exchange import BybitExchange
            
            # 创建Bybit实例（不需要真实API密钥来获取公开的交易规则）
            bybit = BybitExchange(
                api_key="dummy",  # 公开API不需要真实密钥
                api_secret="dummy",
                testnet=False
            )
            
            # 测试交易对
            test_cases = [
                ("ICNT-USDT", "futures"),
                ("SPK-USDT", "spot")
            ]
            
            for symbol, market_type in test_cases:
                case_key = f"{symbol}_{market_type}"
                print(f"\n🧪 获取 {symbol} {market_type} 交易规则...")
                
                try:
                    # 使用Bybit的get_instruments_info方法获取交易规则
                    category = "spot" if market_type == "spot" else "linear"
                    bybit_symbol = symbol.replace('-', '')  # 转换为Bybit格式

                    response = await bybit.get_instruments_info(category, bybit_symbol)

                    if response and "result" in response and "list" in response["result"]:
                        instruments = response["result"]["list"]
                        if instruments:
                            instrument = instruments[0]
                            lot_size_filter = instrument.get("lotSizeFilter", {})
                            price_filter = instrument.get("priceFilter", {})

                            # 根据市场类型获取步长
                            if market_type == "spot":
                                step_size = lot_size_filter.get("basePrecision", "0.001")
                            else:
                                step_size = lot_size_filter.get("qtyStep", "0.001")

                            precision_info = {
                                "step_size": float(step_size),
                                "min_amount": float(lot_size_filter.get("minOrderQty", "0.001")),
                                "max_amount": float(lot_size_filter.get("maxOrderQty", "1000000")),
                                "price_precision": self._calculate_precision(price_filter.get("tickSize", "0.01")),
                                "amount_precision": self._calculate_precision(step_size),
                                "min_notional": float(lot_size_filter.get("minOrderAmt", "5.0")),
                                "source": "api",
                                "raw_data": {
                                    "lotSizeFilter": lot_size_filter,
                                    "priceFilter": price_filter
                                }
                            }
                        else:
                            precision_info = None
                    else:
                        precision_info = None
                    
                    if precision_info:
                        self.results["real_trading_rules"][case_key] = precision_info
                        
                        print(f"✅ {case_key} 真实交易规则:")
                        print(f"   步长 (step_size): {precision_info.get('step_size', 'N/A')}")
                        print(f"   最小数量 (min_amount): {precision_info.get('min_amount', 'N/A')}")
                        print(f"   最大数量 (max_amount): {precision_info.get('max_amount', 'N/A')}")
                        print(f"   价格精度 (price_precision): {precision_info.get('price_precision', 'N/A')}")
                        print(f"   数量精度 (amount_precision): {precision_info.get('amount_precision', 'N/A')}")
                        print(f"   最小名义价值 (min_notional): {precision_info.get('min_notional', 'N/A')}")
                        print(f"   数据源: {precision_info.get('source', 'N/A')}")
                        
                        self.print_result(f"{case_key} 规则获取", True, "成功获取真实交易规则")
                    else:
                        self.results["real_trading_rules"][case_key] = {"error": "无法获取交易规则"}
                        self.print_result(f"{case_key} 规则获取", False, "无法获取交易规则")
                        
                except Exception as e:
                    error_msg = str(e)
                    self.results["real_trading_rules"][case_key] = {"error": error_msg}
                    self.print_result(f"{case_key} 规则获取", False, f"错误: {error_msg}")
                    
        except Exception as e:
            self.print_result("Bybit交易所初始化", False, f"错误: {e}")
            
    def analyze_precision_mismatch(self):
        """分析精度不匹配问题"""
        self.print_header("精度不匹配分析")
        
        # 分析每个交易对的精度问题
        problem_cases = [
            {
                "symbol": "ICNT-USDT",
                "market_type": "futures", 
                "original_amount": 153.307,
                "error_code": "10001",
                "error_message": "Qty invalid"
            },
            {
                "symbol": "SPK-USDT",
                "market_type": "spot",
                "original_amount": 321.995,
                "error_code": "170137", 
                "error_message": "Order quantity has too many decimals"
            }
        ]
        
        for case in problem_cases:
            case_key = f"{case['symbol']}_{case['market_type']}"
            print(f"\n📊 分析 {case_key}:")
            print(f"   原始数量: {case['original_amount']}")
            print(f"   错误代码: {case['error_code']}")
            print(f"   错误信息: {case['error_message']}")
            
            # 获取真实交易规则
            real_rule = self.results["real_trading_rules"].get(case_key, {})
            
            if "error" not in real_rule and real_rule:
                real_step_size = real_rule.get("step_size", "未知")
                real_precision = real_rule.get("amount_precision", "未知")
                
                print(f"   真实步长: {real_step_size}")
                print(f"   真实精度: {real_precision}")
                
                # 分析数量是否符合真实步长
                if real_step_size != "未知":
                    try:
                        amount_decimal = Decimal(str(case['original_amount']))
                        step_decimal = Decimal(str(real_step_size))
                        remainder = amount_decimal % step_decimal
                        is_valid = remainder == 0
                        
                        print(f"   步长合规性: {'✅ 合规' if is_valid else '❌ 不合规'}")
                        if not is_valid:
                            print(f"   余数: {remainder}")
                            # 计算正确的数量
                            correct_amount = (amount_decimal // step_decimal) * step_decimal
                            print(f"   建议数量: {correct_amount}")
                            
                    except Exception as e:
                        print(f"   步长验证错误: {e}")
                        
                # 分析小数位数
                if real_precision != "未知":
                    amount_str = str(case['original_amount'])
                    if '.' in amount_str:
                        decimal_places = len(amount_str.split('.')[1])
                        print(f"   当前小数位: {decimal_places}")
                        print(f"   允许小数位: {real_precision}")
                        print(f"   小数位合规: {'✅ 合规' if decimal_places <= real_precision else '❌ 超出'}")
                        
            else:
                print(f"   ⚠️ 无法获取真实交易规则: {real_rule.get('error', '未知错误')}")
                
            # 存储分析结果
            self.results["precision_comparison"][case_key] = {
                "original_amount": case['original_amount'],
                "error_info": {
                    "code": case['error_code'],
                    "message": case['error_message']
                },
                "real_rule": real_rule,
                "analysis_completed": "error" not in real_rule
            }
            
    def generate_root_cause_analysis(self):
        """生成根本原因分析"""
        self.print_header("根本原因分析")
        
        root_causes = []
        
        # 分析ICNT-USDT期货问题
        icnt_rule = self.results["real_trading_rules"].get("ICNT-USDT_futures", {})
        if "error" not in icnt_rule and icnt_rule:
            real_step = icnt_rule.get("step_size", 0.001)
            if real_step != 0.001:  # 如果真实步长不是默认的0.001
                root_causes.append({
                    "symbol": "ICNT-USDT",
                    "market_type": "futures",
                    "issue": "期货步长不匹配",
                    "details": f"系统使用默认步长0.001，但真实步长是{real_step}",
                    "impact": "导致153.307不符合真实步长要求",
                    "fix": f"更新交易规则预加载器，使用真实步长{real_step}"
                })
        
        # 分析SPK-USDT现货问题  
        spk_rule = self.results["real_trading_rules"].get("SPK-USDT_spot", {})
        if "error" not in spk_rule and spk_rule:
            real_precision = spk_rule.get("amount_precision", 3)
            if real_precision < 3:  # 如果真实精度比默认的3位小数更严格
                root_causes.append({
                    "symbol": "SPK-USDT", 
                    "market_type": "spot",
                    "issue": "现货精度不匹配",
                    "details": f"系统使用默认精度3位小数，但真实精度是{real_precision}位",
                    "impact": "导致321.995小数位过多",
                    "fix": f"更新交易规则预加载器，使用真实精度{real_precision}位小数"
                })
        
        # 通用问题
        if not icnt_rule or not spk_rule or "error" in icnt_rule or "error" in spk_rule:
            root_causes.append({
                "symbol": "通用",
                "market_type": "all", 
                "issue": "交易规则API获取失败",
                "details": "无法从Bybit API获取真实交易规则，系统回退到默认值",
                "impact": "使用不准确的默认精度导致API错误",
                "fix": "修复交易规则API调用，确保能正确获取真实规则"
            })
            
        # 输出分析结果
        for i, cause in enumerate(root_causes, 1):
            print(f"\n🔍 根本原因 {i}: {cause['issue']}")
            print(f"   交易对: {cause['symbol']} {cause['market_type']}")
            print(f"   详细说明: {cause['details']}")
            print(f"   影响: {cause['impact']}")
            print(f"   修复方案: {cause['fix']}")
            
        self.results["root_cause_analysis"] = root_causes
        
    async def run_analysis(self):
        """运行完整分析"""
        print("🚀 开始真实Bybit交易规则检查...")
        
        await self.get_real_bybit_trading_rules()
        self.analyze_precision_mismatch()
        self.generate_root_cause_analysis()
        
        # 保存结果
        results_file = "123/diagnostic_results/real_bybit_rules_analysis.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            
        print(f"\n📊 分析结果已保存到: {results_file}")
        
        # 总结
        print(f"\n{'='*60}")
        print("🎯 分析总结")
        print(f"{'='*60}")
        print(f"🔍 真实交易规则: {len(self.results['real_trading_rules'])}")
        print(f"📊 精度对比: {len(self.results['precision_comparison'])}")
        print(f"🔧 根本原因: {len(self.results['root_cause_analysis'])}")
        print("📋 详细结果请查看JSON文件")

if __name__ == "__main__":
    analysis = RealBybitTradingRulesCheck()
    asyncio.run(analysis.run_analysis())
