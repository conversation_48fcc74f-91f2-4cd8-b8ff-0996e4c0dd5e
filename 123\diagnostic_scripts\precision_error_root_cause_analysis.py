#!/usr/bin/env python3
"""
精度错误根本原因分析
专注于分析为什么153.307和321.995会导致Bybit API错误
"""

import os
import sys
import asyncio
import time
import json
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class PrecisionErrorRootCauseAnalysis:
    def __init__(self):
        self.results = {
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "error_cases": {
                "icnt_futures": {
                    "original_quantity": 153.307,
                    "error_code": "10001",
                    "error_message": "Qty invalid"
                },
                "spk_spot": {
                    "original_quantity": 321.995,
                    "error_code": "170137", 
                    "error_message": "Order quantity has too many decimals"
                }
            },
            "precision_analysis": {},
            "root_cause_identified": False,
            "fix_required": []
        }
        
    def print_header(self, title: str):
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
        
    def analyze_decimal_precision(self, quantity: float, description: str):
        """分析数量的小数精度"""
        print(f"\n📊 分析 {description}: {quantity}")
        
        # 转换为字符串分析小数位
        qty_str = str(quantity)
        if '.' in qty_str:
            decimal_places = len(qty_str.split('.')[1])
            print(f"   小数位数: {decimal_places}")
            print(f"   小数部分: {qty_str.split('.')[1]}")
        else:
            decimal_places = 0
            print(f"   小数位数: 0 (整数)")
            
        # 分析与常见步长的兼容性
        common_step_sizes = [1, 0.1, 0.01, 0.001, 0.0001, 0.00001]
        compatible_steps = []
        
        for step in common_step_sizes:
            remainder = quantity % step
            if abs(remainder) < 1e-10 or abs(remainder - step) < 1e-10:
                compatible_steps.append(step)
                
        print(f"   兼容的步长: {compatible_steps}")
        
        # 检查是否符合0.001步长（系统默认）
        remainder_001 = quantity % 0.001
        is_001_compatible = abs(remainder_001) < 1e-10 or abs(remainder_001 - 0.001) < 1e-10
        print(f"   符合0.001步长: {is_001_compatible}")
        
        if not is_001_compatible:
            print(f"   ⚠️ 不符合默认步长0.001！余数: {remainder_001}")
            
        return {
            "decimal_places": decimal_places,
            "compatible_steps": compatible_steps,
            "is_001_compatible": is_001_compatible,
            "remainder_001": remainder_001
        }
        
    async def analyze_trading_rules_source(self):
        """分析交易规则来源"""
        self.print_header("交易规则来源分析")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 分析ICNT-USDT期货规则
            print(f"\n🧪 分析 ICNT-USDT 期货交易规则...")
            try:
                icnt_rule = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
                if icnt_rule:
                    print(f"   步长: {icnt_rule.qty_step}")
                    print(f"   来源: {icnt_rule.source}")
                    print(f"   最小数量: {icnt_rule.min_qty}")
                    print(f"   最大数量: {icnt_rule.max_qty}")

                    self.results["precision_analysis"]["icnt_futures"] = {
                        "rule_found": True,
                        "step_size": float(icnt_rule.qty_step),
                        "source": icnt_rule.source,
                        "min_amount": float(icnt_rule.min_qty),
                        "max_amount": float(icnt_rule.max_qty)
                    }

                    # 检查153.307是否符合规则
                    step_size = float(icnt_rule.qty_step)
                    remainder = 153.307 % step_size
                    is_valid = abs(remainder) < 1e-10 or abs(remainder - step_size) < 1e-10

                    print(f"   153.307 % {step_size} = {remainder}")
                    print(f"   数量有效性: {'✅ 有效' if is_valid else '❌ 无效'}")

                    if not is_valid:
                        print(f"   🚨 这解释了为什么会出现 '10001: Qty invalid' 错误！")
                        
                else:
                    print(f"   ❌ 未找到交易规则")
                    self.results["precision_analysis"]["icnt_futures"] = {"rule_found": False}
                    
            except Exception as e:
                print(f"   ❌ 获取规则失败: {e}")
                self.results["precision_analysis"]["icnt_futures"] = {"error": str(e)}
                
            # 分析SPK-USDT现货规则
            print(f"\n🧪 分析 SPK-USDT 现货交易规则...")
            try:
                spk_rule = preloader.get_trading_rule("bybit", "SPK-USDT", "spot")
                if spk_rule:
                    print(f"   步长: {spk_rule.qty_step}")
                    print(f"   来源: {spk_rule.source}")
                    print(f"   最小数量: {spk_rule.min_qty}")
                    print(f"   最大数量: {spk_rule.max_qty}")

                    self.results["precision_analysis"]["spk_spot"] = {
                        "rule_found": True,
                        "step_size": float(spk_rule.qty_step),
                        "source": spk_rule.source,
                        "min_amount": float(spk_rule.min_qty),
                        "max_amount": float(spk_rule.max_qty)
                    }

                    # 检查321.995是否符合规则
                    step_size = float(spk_rule.qty_step)
                    remainder = 321.995 % step_size
                    is_valid = abs(remainder) < 1e-10 or abs(remainder - step_size) < 1e-10

                    print(f"   321.995 % {step_size} = {remainder}")
                    print(f"   数量有效性: {'✅ 有效' if is_valid else '❌ 无效'}")

                    if not is_valid:
                        print(f"   🚨 这解释了为什么会出现 '170137: Order quantity has too many decimals' 错误！")
                        
                else:
                    print(f"   ❌ 未找到交易规则")
                    self.results["precision_analysis"]["spk_spot"] = {"rule_found": False}
                    
            except Exception as e:
                print(f"   ❌ 获取规则失败: {e}")
                self.results["precision_analysis"]["spk_spot"] = {"error": str(e)}
                
        except Exception as e:
            print(f"❌ 交易规则预加载器初始化失败: {e}")
            
    def analyze_quantity_formatting(self):
        """分析数量格式化过程"""
        self.print_header("数量格式化过程分析")
        
        # 分析153.307的格式化
        print(f"\n📊 分析153.307的格式化过程:")
        self.analyze_decimal_precision(153.307, "ICNT-USDT期货数量")
        
        # 分析321.995的格式化
        print(f"\n📊 分析321.995的格式化过程:")
        self.analyze_decimal_precision(321.995, "SPK-USDT现货数量")
        
    def identify_root_cause(self):
        """识别根本原因"""
        self.print_header("根本原因识别")
        
        # 检查是否两个案例都使用默认规则
        icnt_source = self.results["precision_analysis"].get("icnt_futures", {}).get("source")
        spk_source = self.results["precision_analysis"].get("spk_spot", {}).get("source")
        
        print(f"📊 规则来源分析:")
        print(f"   ICNT-USDT期货规则来源: {icnt_source}")
        print(f"   SPK-USDT现货规则来源: {spk_source}")
        
        if icnt_source == "default" and spk_source == "default":
            print(f"\n🚨 根本原因确认: 两个交易对都在使用默认规则！")
            print(f"   系统未能从Bybit API获取真实的交易规则")
            print(f"   回退到默认的step_size=0.001，但这与Bybit真实要求不符")
            
            self.results["root_cause_identified"] = True
            self.results["fix_required"].append({
                "issue": "交易规则API获取失败",
                "cause": "系统使用默认规则而非真实API规则",
                "impact": "数量精度不符合Bybit真实要求",
                "fix": "修复交易规则预加载器的API调用",
                "priority": "CRITICAL"
            })
            
        # 分析具体的精度不匹配
        print(f"\n🔍 精度不匹配分析:")
        
        # 153.307的问题
        remainder_153 = 153.307 % 0.001
        if abs(remainder_153) > 1e-10 and abs(remainder_153 - 0.001) > 1e-10:
            print(f"   153.307不符合0.001步长，余数: {remainder_153}")
            print(f"   这解释了Bybit '10001: Qty invalid' 错误")
            
        # 321.995的问题  
        remainder_321 = 321.995 % 0.001
        if abs(remainder_321) > 1e-10 and abs(remainder_321 - 0.001) > 1e-10:
            print(f"   321.995不符合0.001步长，余数: {remainder_321}")
            print(f"   这解释了Bybit '170137: Order quantity has too many decimals' 错误")
            
    async def run_analysis(self):
        """运行完整分析"""
        print("🚀 开始精度错误根本原因分析...")
        
        self.analyze_quantity_formatting()
        await self.analyze_trading_rules_source()
        self.identify_root_cause()
        
        # 保存结果
        results_file = "123/diagnostic_results/precision_error_root_cause_analysis.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            
        print(f"\n📊 分析结果已保存到: {results_file}")
        
        # 总结
        print(f"\n{'='*60}")
        print("🎯 根本原因总结")
        print(f"{'='*60}")
        
        if self.results["root_cause_identified"]:
            print("✅ 根本原因已确认:")
            for fix in self.results["fix_required"]:
                print(f"   🚨 {fix['issue']}: {fix['cause']}")
                print(f"   🔧 修复方案: {fix['fix']}")
        else:
            print("⚠️ 需要进一步调查")
            
        print(f"\n🎯 关键发现:")
        print(f"   - 两个错误都是精度相关的API错误")
        print(f"   - 系统使用默认规则而非真实Bybit规则")
        print(f"   - 需要修复交易规则预加载器的API调用逻辑")

if __name__ == "__main__":
    analysis = PrecisionErrorRootCauseAnalysis()
    asyncio.run(analysis.run_analysis())
