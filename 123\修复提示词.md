友情提示： 禁止模拟数据！
-### **套利流程**: 达到期货溢价（+）阈值开仓 →锁定差价-> 等待趋同 → 现货溢价（-）达到阈值-> 平仓
+ 代表期货溢价   -代表现货溢价 不代表负数！
- **一切以### **（通用系统支持任意代币的角度来）**深度审查修复！  

重点任务：  精准定位bug原因！ ，并且 其他交易所有没有这个以下问题！ 不要过度修复！

2025-07-30 18:00:41.166 [ERROR] [ExecutionEngine] ❌ 期货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 10001: Qty invalid'}", execution_time_ms=439.53847885131836, params_used=OpeningOrderParams(symbol='ICNT-USDT', side='sell', order_type='market', quantity='153.307', price=None, market_type='futures', original_quantity=153.307, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))


2025-07-30 18:01:38.272 [ERROR] [ExecutionEngine] ❌ 现货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}", execution_time_ms=267.10033416748047, params_used=OpeningOrderParams(symbol='SPK-USDT', side='buy', order_type='market', quantity='321.995', price=None, market_type='spot', original_quantity=321.995, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))



 所有的修复和优化必须- 考虑（一致性）和（高速性能）和（差价精准度）！核心重点#######
✅ 联动检查所有交易所（如：Bybit、Gate、OKX） ✅ 联动横向检查 确保系统完全一致！没有漏洞！






第一：精准定位问题： 
1. 从头到尾查看docs 中的 所有md文档，按序号 进行查看！ 尤其是 07文档！和07B 修复记录， 多看文档确保足够了解！ ，禁止造轮子，严格按照要求来实现！
2. 深度检查实际代码！进行手动审查 
3. 手动审查完毕后，创建精确的诊断脚本，精准定位错误！ 一直到找到问题所在，进行精准修复！ 
 
 

第二：深度思考问题： 
## 📋 每次检查完毕后，你必须回答的问题（内部检查清单） 
1. 现有架构中是否已有此功能？（引用【系统核心知识库】中的模块列表回答） 
2. 是否应该在统一模块中实现？（统一模块） 
3. 问题的根本原因是什么？（基于【精准定位问题】的分析） 
4. 检查链路和接口的结果是什么？（基于【精准定位问题】的分析） 
5. 其他两个交易所是否有同样问题？（基于【精准定位问题】的分析） 
6. 如何从源头最优解决问题？（基于【统一修复】的原则） 
7. 是否重复调用，存在造轮子！（进行对比，优质整合删除。） 
8. 横向深度全面查阅资料并思考！ 确保万无一失！ （包括doc 中的md文档，和 官方SDK在项目内） 永远不要忘记，这是个通用多代币期货溢价套利！ 
 



第三：优化规则和要求 
🔗 链路完整性与一致性优化： 
1. 所有接口参数、入参顺序、命名风格必须保持统一； 
2. 严格避免链路中断（如：调用丢失、上下游类型不匹配、数据未透传）； 
3. 自动合并冗余的调用路径或重复链路，提高结构简洁性与稳定性； 
4. 进行优化后！自动更新doc 中的md文件 修复记录更新到123\docs\07B_核心问题修复专项文档.md  功能新增更新到123\docs\07_全流程工作流文档.md 保持07_全流程工作流的权威性和整洁。 
5. 禁止使用修复脚本进行修复，必须手动修复！
6. ## 100确%确定?修复优化没有造车轮?? 使用了统一模块？没有引入新的问题？ 完美修复？ 确保功能实现？？ 职责清晰，没有重复，没有冗余，没有接口不统一 接口不兼容！链路错误！并且测试非常权威没有问题？ 

  
 
✅ 修复后验证机制：

1. 所有的测试必须确保是机构级别高质量测试！！必须覆盖多交易所一致性、系统性能、通用性，并确保上下游模块全部联动测试无误。测试分为三段进阶验证机制：
① 基础核心测试：模块单元功能验证（如：参数输入输出、边界检查、错误处理），确保修复点本身100%稳定；
② 复杂系统级联测试：涉及模块之间的交互逻辑、状态联动、多币种切换、多交易所分支，验证系统协同一致性；
③ 生产环境仿真测试：真实订单簿、真实API响应、网络波动模拟、多任务并发压力、极限滑点与稀有差价场景回放，确保部署到实盘零失误。
并且所有测试必须 100% 通过，没有任何问题！ 最常见的问题就是测试全部通过，实盘却立即出错！ 所以必须支持自动运行测试，输出结果、覆盖率、成功状态，不容遗漏、不准掩盖！
2. 禁止虚假测试，测试完毕后仔细查看结果！ 
3. 测试结果必须使用json格式，因为环境问题经常会测试卡住，我会手动取消，你需要第一时间查看json文件虽然卡住，但是有结果，