#!/usr/bin/env python3
"""
清除缓存并重新测试交易规则
"""

import os
import sys
import asyncio
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def test_with_cache_clear():
    print("🚀 清除缓存并重新测试交易规则...")
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        
        preloader = get_trading_rules_preloader()
        
        # 清除所有缓存
        print("🧹 清除交易规则缓存...")
        preloader.trading_rules.clear()
        preloader.unsupported_pairs.clear()
        
        print(f"   缓存清除后，交易规则数量: {len(preloader.trading_rules)}")
        print(f"   不支持交易对数量: {len(preloader.unsupported_pairs)}")
        
        # 重新获取ICNT期货规则
        print(f"\n🧪 重新获取ICNT-USDT期货规则...")
        icnt_rule = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
        
        if icnt_rule:
            print(f"✅ ICNT期货规则获取成功:")
            print(f"   步长: {icnt_rule.qty_step}")
            print(f"   来源: {icnt_rule.source}")
            print(f"   最小数量: {icnt_rule.min_qty}")
            print(f"   最大数量: {icnt_rule.max_qty}")
            
            # 验证步长
            if float(icnt_rule.qty_step) == 1.0 and icnt_rule.source == "api":
                print(f"   ✅ 规则正确！步长=1.0，来源=api")
            else:
                print(f"   🚨 规则异常！步长={icnt_rule.qty_step}，来源={icnt_rule.source}")
                
            # 测试数量格式化
            print(f"\n🧪 测试数量格式化...")
            test_amounts = [153.307, 153.0, 154.0, 153.5]
            
            for amount in test_amounts:
                try:
                    formatted = preloader.format_amount_unified("bybit", "ICNT-USDT", "futures", amount)
                    print(f"   {amount} → '{formatted}'")
                    
                    # 验证格式化结果
                    formatted_float = float(formatted)
                    remainder = formatted_float % 1.0
                    is_valid = abs(remainder) < 1e-10
                    
                    print(f"      有效性: {'✅' if is_valid else '❌'} (余数: {remainder})")
                    
                except Exception as e:
                    print(f"   {amount} → 格式化失败: {e}")
                    
        else:
            print(f"❌ ICNT期货规则获取失败")
            
        # 重新获取SPK现货规则
        print(f"\n🧪 重新获取SPK-USDT现货规则...")
        spk_rule = preloader.get_trading_rule("bybit", "SPK-USDT", "spot")
        
        if spk_rule:
            print(f"✅ SPK现货规则获取成功:")
            print(f"   步长: {spk_rule.qty_step}")
            print(f"   来源: {spk_rule.source}")
            print(f"   最小数量: {spk_rule.min_qty}")
            print(f"   最大数量: {spk_rule.max_qty}")
            
            # 验证步长
            if float(spk_rule.qty_step) == 0.1 and spk_rule.source == "api":
                print(f"   ✅ 规则正确！步长=0.1，来源=api")
            else:
                print(f"   🚨 规则异常！步长={spk_rule.qty_step}，来源={spk_rule.source}")
                
            # 测试数量格式化
            print(f"\n🧪 测试数量格式化...")
            test_amounts = [321.995, 321.9, 322.0, 321.8]
            
            for amount in test_amounts:
                try:
                    formatted = preloader.format_amount_unified("bybit", "SPK-USDT", "spot", amount)
                    print(f"   {amount} → '{formatted}'")
                    
                    # 验证格式化结果
                    formatted_float = float(formatted)
                    remainder = formatted_float % 0.1
                    is_valid = abs(remainder) < 1e-10 or abs(remainder - 0.1) < 1e-10
                    
                    print(f"      有效性: {'✅' if is_valid else '❌'} (余数: {remainder})")
                    
                except Exception as e:
                    print(f"   {amount} → 格式化失败: {e}")
                    
        else:
            print(f"❌ SPK现货规则获取失败")
            
        # 总结
        print(f"\n{'='*60}")
        print("🎯 测试总结")
        print(f"{'='*60}")
        
        if icnt_rule and float(icnt_rule.qty_step) == 1.0 and icnt_rule.source == "api":
            print(f"✅ ICNT期货规则修复成功")
        else:
            print(f"❌ ICNT期货规则仍有问题")
            
        if spk_rule and float(spk_rule.qty_step) == 0.1 and spk_rule.source == "api":
            print(f"✅ SPK现货规则修复成功")
        else:
            print(f"❌ SPK现货规则仍有问题")
            
        print(f"\n🔧 修复建议:")
        print(f"   1. 确保系统启动时清除旧的错误缓存")
        print(f"   2. 使用正确的API规则进行数量格式化")
        print(f"   3. 153.307应该格式化为153或154（整数）")
        print(f"   4. 321.995应该格式化为321.9或322.0（0.1的倍数）")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_with_cache_clear())
