#!/usr/bin/env python3
"""
精确数量错误诊断脚本
专门诊断ICNT-USDT和SPK-USDT的数量精度问题

错误信息：
1. ICNT-USDT期货: Bybit API错误: 10001: Qty invalid (153.307)
2. SPK-USDT现货: Bybit API错误: 170137: Order quantity has too many decimals (321.995)
"""

import os
import sys
import asyncio
import time
from decimal import Decimal
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class PreciseQuantityErrorDiagnosis:
    def __init__(self):
        self.results = {
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_cases": {},
            "precision_analysis": {},
            "fix_recommendations": []
        }
        
    def print_header(self, title: str):
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
        
    def print_result(self, test_name: str, passed: bool, details: str = ""):
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
        
    async def diagnose_trading_rules_precision(self):
        """诊断交易规则精度获取"""
        self.print_header("交易规则精度诊断")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试问题交易对
            test_cases = [
                ("ICNT-USDT", "bybit", "futures", 153.307),
                ("SPK-USDT", "bybit", "spot", 321.995)
            ]
            
            for symbol, exchange, market_type, test_amount in test_cases:
                case_key = f"{symbol}_{exchange}_{market_type}"
                case_result = {
                    "symbol": symbol,
                    "exchange": exchange,
                    "market_type": market_type,
                    "test_amount": test_amount,
                    "rule_found": False,
                    "rule_details": None,
                    "formatted_amount": None,
                    "precision_valid": False,
                    "error": None
                }
                
                try:
                    # 获取交易规则
                    rule = preloader.get_trading_rule(exchange, symbol, market_type)
                    if rule:
                        case_result["rule_found"] = True
                        case_result["rule_details"] = {
                            "qty_step": str(rule.qty_step),
                            "price_step": str(rule.price_step),
                            "qty_precision": rule.qty_precision,
                            "min_qty": str(rule.min_qty),
                            "max_qty": str(rule.max_qty),
                            "source": rule.source
                        }
                        
                        # 测试数量格式化
                        if market_type == "futures":
                            # 期货使用合约转换方法
                            formatted = await preloader.format_amount_with_contract_conversion(
                                test_amount, exchange, symbol, market_type
                            )
                        else:
                            # 现货使用统一格式化方法
                            formatted = preloader.format_amount_unified(
                                test_amount, exchange, symbol, market_type
                            )
                        
                        case_result["formatted_amount"] = formatted
                        
                        # 验证精度是否正确
                        formatted_decimal = Decimal(formatted)
                        step_decimal = Decimal(str(rule.qty_step))
                        remainder = formatted_decimal % step_decimal
                        case_result["precision_valid"] = remainder == 0
                        
                        self.print_result(
                            f"{case_key} 规则获取", 
                            True, 
                            f"步长={rule.qty_step}, 格式化={formatted}, 精度={'有效' if case_result['precision_valid'] else '无效'}"
                        )
                        
                    else:
                        case_result["error"] = "交易规则未找到"
                        self.print_result(f"{case_key} 规则获取", False, "交易规则未找到")
                        
                except Exception as e:
                    case_result["error"] = str(e)
                    self.print_result(f"{case_key} 规则获取", False, f"错误: {e}")
                
                self.results["test_cases"][case_key] = case_result
                
        except Exception as e:
            self.print_result("交易规则预加载器", False, f"初始化失败: {e}")
            
    async def diagnose_bybit_precision_handling(self):
        """诊断Bybit精度处理逻辑"""
        self.print_header("Bybit精度处理诊断")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试不同的数量值
            test_amounts = [153.307, 321.995, 100.0, 0.001, 0.0001]
            
            for amount in test_amounts:
                print(f"\n🧪 测试数量: {amount}")
                
                # 测试ICNT-USDT期货
                try:
                    icnt_formatted = await preloader.format_amount_with_contract_conversion(
                        amount, "bybit", "ICNT-USDT", "futures"
                    )
                    self.print_result(
                        f"ICNT-USDT期货格式化({amount})", 
                        True, 
                        f"结果: {icnt_formatted}"
                    )
                except Exception as e:
                    self.print_result(
                        f"ICNT-USDT期货格式化({amount})", 
                        False, 
                        f"错误: {e}"
                    )
                
                # 测试SPK-USDT现货
                try:
                    spk_formatted = preloader.format_amount_unified(
                        amount, "bybit", "SPK-USDT", "spot"
                    )
                    self.print_result(
                        f"SPK-USDT现货格式化({amount})", 
                        True, 
                        f"结果: {spk_formatted}"
                    )
                except Exception as e:
                    self.print_result(
                        f"SPK-USDT现货格式化({amount})", 
                        False, 
                        f"错误: {e}"
                    )
                    
        except Exception as e:
            self.print_result("Bybit精度处理", False, f"初始化失败: {e}")
            
    def analyze_precision_requirements(self):
        """分析精度要求"""
        self.print_header("精度要求分析")
        
        # 分析错误信息
        error_analysis = {
            "ICNT-USDT_futures": {
                "original_amount": 153.307,
                "error_code": "10001",
                "error_message": "Qty invalid",
                "analysis": "数量153.307可能不符合Bybit期货的步长要求",
                "possible_causes": [
                    "步长不是0.001的整数倍",
                    "合约转换后的数量不正确",
                    "精度处理有误"
                ]
            },
            "SPK-USDT_spot": {
                "original_amount": 321.995,
                "error_code": "170137", 
                "error_message": "Order quantity has too many decimals",
                "analysis": "数量321.995小数位过多，超过了Bybit现货允许的精度",
                "possible_causes": [
                    "小数位数超过basePrecision要求",
                    "尾随零处理不正确",
                    "精度截取逻辑有误"
                ]
            }
        }
        
        for case, analysis in error_analysis.items():
            print(f"\n📊 {case} 错误分析:")
            print(f"   原始数量: {analysis['original_amount']}")
            print(f"   错误代码: {analysis['error_code']}")
            print(f"   错误信息: {analysis['error_message']}")
            print(f"   分析结果: {analysis['analysis']}")
            print(f"   可能原因:")
            for cause in analysis['possible_causes']:
                print(f"     - {cause}")
                
        self.results["precision_analysis"] = error_analysis
        
    def generate_fix_recommendations(self):
        """生成修复建议"""
        self.print_header("修复建议")
        
        recommendations = [
            {
                "issue": "ICNT-USDT期货数量无效",
                "root_cause": "期货合约转换或步长处理不正确",
                "fix_steps": [
                    "检查ICNT-USDT期货的实际步长要求",
                    "验证format_amount_with_contract_conversion方法的合约转换逻辑",
                    "确保数量是步长的整数倍",
                    "添加Bybit期货特殊处理逻辑"
                ],
                "code_location": "core/trading_rules_preloader.py:format_amount_with_contract_conversion",
                "priority": "HIGH"
            },
            {
                "issue": "SPK-USDT现货小数位过多",
                "root_cause": "现货精度处理不符合Bybit basePrecision要求",
                "fix_steps": [
                    "检查SPK-USDT现货的basePrecision设置",
                    "修复format_amount_unified方法的小数位处理",
                    "应用Bybit尾随零修复逻辑",
                    "确保精度截取而非四舍五入"
                ],
                "code_location": "core/trading_rules_preloader.py:format_amount_unified",
                "priority": "HIGH"
            },
            {
                "issue": "通用精度处理不一致",
                "root_cause": "现货和期货使用不同的精度处理逻辑",
                "fix_steps": [
                    "统一现货basePrecision和期货qtyStep的处理",
                    "确保所有Bybit交易对都应用尾随零修复",
                    "添加精度验证和错误处理",
                    "实施统一的步长合规检查"
                ],
                "code_location": "core/trading_rules_preloader.py:_get_bybit_trading_rule",
                "priority": "MEDIUM"
            }
        ]
        
        for i, rec in enumerate(recommendations, 1):
            print(f"\n🔧 修复建议 {i}: {rec['issue']}")
            print(f"   根本原因: {rec['root_cause']}")
            print(f"   修复步骤:")
            for step in rec['fix_steps']:
                print(f"     {step}")
            print(f"   代码位置: {rec['code_location']}")
            print(f"   优先级: {rec['priority']}")
            
        self.results["fix_recommendations"] = recommendations
        
    async def run_diagnosis(self):
        """运行完整诊断"""
        print("🚀 开始精确数量错误诊断...")
        
        await self.diagnose_trading_rules_precision()
        await self.diagnose_bybit_precision_handling()
        self.analyze_precision_requirements()
        self.generate_fix_recommendations()
        
        # 保存结果
        import json
        results_file = "123/diagnostic_results/quantity_error_diagnosis.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            
        print(f"\n📊 诊断结果已保存到: {results_file}")
        
        # 总结
        print(f"\n{'='*60}")
        print("🎯 诊断总结")
        print(f"{'='*60}")
        print(f"✅ 测试用例: {len(self.results['test_cases'])}")
        print(f"📊 精度分析: {len(self.results['precision_analysis'])}")
        print(f"🔧 修复建议: {len(self.results['fix_recommendations'])}")
        print("🔍 详细结果请查看JSON文件")

if __name__ == "__main__":
    diagnosis = PreciseQuantityErrorDiagnosis()
    asyncio.run(diagnosis.run_diagnosis())
