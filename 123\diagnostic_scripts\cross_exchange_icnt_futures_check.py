#!/usr/bin/env python3
"""
跨交易所ICNT-USDT期货支持检查
验证Gate.io、Bybit、OKX是否支持ICNT-USDT期货合约
"""

import os
import sys
import asyncio
import time
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class CrossExchangeICNTFuturesCheck:
    def __init__(self):
        self.results = {
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "icnt_futures_support": {},
            "root_cause_confirmed": False,
            "critical_findings": []
        }
        
    def print_header(self, title: str):
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
        
    def print_result(self, test_name: str, passed: bool, details: str = ""):
        status = "✅ SUPPORTED" if passed else "❌ NOT_SUPPORTED"
        print(f"{status} {test_name}: {details}")
        
    async def check_gate_icnt_futures(self):
        """检查Gate.io ICNT-USDT期货支持"""
        print(f"\n🧪 检查 Gate.io ICNT-USDT 期货...")
        
        try:
            from exchanges.gate_exchange import GateExchange
            
            gate = GateExchange(
                api_key="dummy",
                api_secret="dummy"
            )
            
            # 尝试获取ICNT-USDT期货合约信息
            try:
                # Gate.io使用ICNT_USDT格式
                response = await gate.get_futures_contracts("ICNT_USDT")
                
                if response and len(response) > 0:
                    contract = response[0]
                    self.results["icnt_futures_support"]["gate"] = {
                        "supported": True,
                        "contract_info": {
                            "name": contract.get("name", "unknown"),
                            "size": contract.get("size", "unknown"),
                            "quanto_multiplier": contract.get("quanto_multiplier", "unknown"),
                            "leverage_min": contract.get("leverage_min", "unknown"),
                            "leverage_max": contract.get("leverage_max", "unknown")
                        }
                    }
                    self.print_result(
                        "Gate.io ICNT-USDT期货", 
                        True, 
                        f"合约名称: {contract.get('name', 'unknown')}"
                    )
                else:
                    self.results["icnt_futures_support"]["gate"] = {
                        "supported": False,
                        "error": "合约不存在"
                    }
                    self.print_result("Gate.io ICNT-USDT期货", False, "合约不存在")
                    
            except Exception as e:
                error_str = str(e)
                
                if "INVALID_SYMBOL" in error_str or "symbol not found" in error_str:
                    self.results["icnt_futures_support"]["gate"] = {
                        "supported": False,
                        "error": "交易对不存在"
                    }
                    self.print_result("Gate.io ICNT-USDT期货", False, "交易对不存在")
                else:
                    self.results["icnt_futures_support"]["gate"] = {
                        "supported": "unknown",
                        "error": error_str
                    }
                    self.print_result("Gate.io ICNT-USDT期货", False, f"API错误: {error_str}")
                    
        except Exception as e:
            self.print_result("Gate.io交易所初始化", False, f"错误: {e}")
            
    async def check_bybit_icnt_futures(self):
        """检查Bybit ICNT-USDT期货支持"""
        print(f"\n🧪 检查 Bybit ICNT-USDT 期货...")
        
        # 基于之前的分析，我们已经知道Bybit不支持
        self.results["icnt_futures_support"]["bybit"] = {
            "supported": False,
            "error": "已确认不存在",
            "evidence": "网络搜索和API测试均确认Bybit只有ICNT/USDT现货，没有期货"
        }
        self.print_result("Bybit ICNT-USDT期货", False, "已确认不存在")
        
    async def check_okx_icnt_futures(self):
        """检查OKX ICNT-USDT期货支持"""
        print(f"\n🧪 检查 OKX ICNT-USDT 期货...")
        
        try:
            from exchanges.okx_exchange import OKXExchange
            
            okx = OKXExchange(
                api_key="dummy",
                api_secret="dummy",
                api_passphrase="dummy"
            )
            
            # 尝试获取ICNT-USDT期货合约信息
            try:
                # OKX使用ICNT-USDT-SWAP格式
                response = await okx.get_instruments("SWAP", "ICNT-USDT-SWAP")
                
                if response and "data" in response and len(response["data"]) > 0:
                    instrument = response["data"][0]
                    self.results["icnt_futures_support"]["okx"] = {
                        "supported": True,
                        "contract_info": {
                            "instId": instrument.get("instId", "unknown"),
                            "ctVal": instrument.get("ctVal", "unknown"),
                            "ctMult": instrument.get("ctMult", "unknown"),
                            "minSz": instrument.get("minSz", "unknown"),
                            "lotSz": instrument.get("lotSz", "unknown")
                        }
                    }
                    self.print_result(
                        "OKX ICNT-USDT期货", 
                        True, 
                        f"合约ID: {instrument.get('instId', 'unknown')}"
                    )
                else:
                    self.results["icnt_futures_support"]["okx"] = {
                        "supported": False,
                        "error": "合约不存在"
                    }
                    self.print_result("OKX ICNT-USDT期货", False, "合约不存在")
                    
            except Exception as e:
                error_str = str(e)
                
                if "51001" in error_str or "Instrument ID does not exist" in error_str:
                    self.results["icnt_futures_support"]["okx"] = {
                        "supported": False,
                        "error": "合约ID不存在"
                    }
                    self.print_result("OKX ICNT-USDT期货", False, "合约ID不存在")
                else:
                    self.results["icnt_futures_support"]["okx"] = {
                        "supported": "unknown",
                        "error": error_str
                    }
                    self.print_result("OKX ICNT-USDT期货", False, f"API错误: {error_str}")
                    
        except Exception as e:
            self.print_result("OKX交易所初始化", False, f"错误: {e}")
            
    def analyze_critical_findings(self):
        """分析关键发现"""
        self.print_header("关键发现分析")
        
        supported_exchanges = []
        unsupported_exchanges = []
        unknown_exchanges = []
        
        for exchange, info in self.results["icnt_futures_support"].items():
            if info.get("supported") == True:
                supported_exchanges.append(exchange)
            elif info.get("supported") == False:
                unsupported_exchanges.append(exchange)
            else:
                unknown_exchanges.append(exchange)
                
        print(f"📊 ICNT-USDT期货支持情况:")
        print(f"   ✅ 支持的交易所: {supported_exchanges if supported_exchanges else '无'}")
        print(f"   ❌ 不支持的交易所: {unsupported_exchanges}")
        print(f"   ❓ 未确定的交易所: {unknown_exchanges}")
        
        # 关键发现1: 如果所有交易所都不支持
        if not supported_exchanges and len(unsupported_exchanges) >= 2:
            print(f"\n🚨 关键发现1: ICNT-USDT期货在主要交易所均不支持！")
            print(f"   这是导致 '10001: Qty invalid' 错误的根本原因")
            print(f"   系统配置了不存在的期货合约进行套利交易")
            
            self.results["root_cause_confirmed"] = True
            self.results["critical_findings"].append({
                "finding": "ICNT-USDT期货普遍不支持",
                "impact": "系统试图在不存在的合约上执行套利策略",
                "severity": "CRITICAL",
                "action_required": "从系统配置中移除ICNT-USDT期货交易"
            })
            
        # 关键发现2: 如果只有部分交易所支持
        elif supported_exchanges and unsupported_exchanges:
            print(f"\n⚠️ 关键发现2: ICNT-USDT期货支持不一致")
            print(f"   支持: {supported_exchanges}")
            print(f"   不支持: {unsupported_exchanges}")
            print(f"   这会导致套利策略无法正常执行")
            
            self.results["critical_findings"].append({
                "finding": "ICNT-USDT期货支持不一致",
                "impact": "套利策略无法在所有配置的交易所间执行",
                "severity": "HIGH",
                "action_required": "调整交易所配置或选择其他交易对"
            })
            
    def generate_fix_recommendations(self):
        """生成修复建议"""
        self.print_header("修复建议")
        
        if self.results["root_cause_confirmed"]:
            print("🔧 立即修复建议:")
            print("1. 从TARGET_SYMBOLS环境变量中移除ICNT-USDT")
            print("2. 重启系统以应用新的交易对配置")
            print("3. 验证其他交易对的期货支持情况")
            print("4. 添加交易对存在性验证机制")
            
            print(f"\n📝 具体操作步骤:")
            print("1. 编辑.env文件")
            print("2. 将TARGET_SYMBOLS中的ICNT-USDT移除")
            print("3. 例如: TARGET_SYMBOLS=SPK-USDT,RESOLV-USDT,CAKE-USDT,WIF-USDT,...")
            print("4. 重启套利系统")
        else:
            print("⚠️ 需要进一步调查以确定最佳修复方案")
            
    async def run_check(self):
        """运行完整检查"""
        print("🚀 开始跨交易所ICNT-USDT期货支持检查...")
        
        await self.check_gate_icnt_futures()
        await self.check_bybit_icnt_futures()
        await self.check_okx_icnt_futures()
        
        self.analyze_critical_findings()
        self.generate_fix_recommendations()
        
        # 保存结果
        results_file = "123/diagnostic_results/cross_exchange_icnt_futures_check.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            
        print(f"\n📊 检查结果已保存到: {results_file}")
        
        # 总结
        print(f"\n{'='*60}")
        print("🎯 检查总结")
        print(f"{'='*60}")
        print(f"🔍 交易所检查: {len(self.results['icnt_futures_support'])}")
        print(f"🚨 根本原因确认: {'是' if self.results['root_cause_confirmed'] else '否'}")
        print(f"🔧 关键发现: {len(self.results['critical_findings'])}")
        
        if self.results["root_cause_confirmed"]:
            print("✅ 根本原因已确认，可以开始修复")
        else:
            print("⚠️ 需要进一步调查")

if __name__ == "__main__":
    check = CrossExchangeICNTFuturesCheck()
    asyncio.run(check.run_check())
