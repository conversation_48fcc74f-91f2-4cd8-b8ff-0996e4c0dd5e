#!/usr/bin/env python3
"""
简化的Bybit API测试
直接获取真实的交易规则
"""

import os
import sys
import asyncio
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def test_bybit_real_rules():
    print("🚀 开始获取Bybit真实交易规则...")
    
    try:
        from exchanges.bybit_exchange import BybitExchange
        
        # 创建Bybit实例
        bybit = BybitExchange(
            api_key="dummy",
            api_secret="dummy", 
            testnet=False
        )
        
        results = {}
        
        # 测试ICNT-USDT期货
        print(f"\n🧪 测试 ICNT-USDT 期货...")
        try:
            response = await bybit.get_instruments_info("linear", "ICNTUSDT")
            
            if response and "result" in response and "list" in response["result"]:
                instruments = response["result"]["list"]
                if instruments:
                    instrument = instruments[0]
                    lot_size_filter = instrument.get("lotSizeFilter", {})
                    
                    qty_step = lot_size_filter.get('qtyStep', 'unknown')
                    min_qty = lot_size_filter.get('minOrderQty', 'unknown')
                    
                    print(f"✅ ICNT期货存在!")
                    print(f"   真实qtyStep: {qty_step}")
                    print(f"   真实minOrderQty: {min_qty}")
                    print(f"   系统默认step_size: 0.001")
                    
                    if str(qty_step) != "0.001":
                        print(f"   🚨 步长不匹配！真实={qty_step}, 默认=0.001")
                        print(f"   🚨 这解释了为什么153.307会导致 '10001: Qty invalid' 错误！")
                        
                    results["icnt_futures"] = {
                        "exists": True,
                        "real_qty_step": qty_step,
                        "real_min_qty": min_qty,
                        "default_step": "0.001",
                        "step_mismatch": str(qty_step) != "0.001"
                    }
                else:
                    print(f"❌ ICNT期货不存在")
                    results["icnt_futures"] = {"exists": False}
            else:
                print(f"❌ API响应异常")
                results["icnt_futures"] = {"exists": False, "error": "API响应异常"}
                
        except Exception as e:
            print(f"❌ ICNT期货测试失败: {e}")
            results["icnt_futures"] = {"exists": False, "error": str(e)}
            
        # 测试SPK-USDT现货
        print(f"\n🧪 测试 SPK-USDT 现货...")
        try:
            response = await bybit.get_instruments_info("spot", "SPKUSDT")
            
            if response and "result" in response and "list" in response["result"]:
                instruments = response["result"]["list"]
                if instruments:
                    instrument = instruments[0]
                    lot_size_filter = instrument.get("lotSizeFilter", {})
                    
                    base_precision = lot_size_filter.get('basePrecision', 'unknown')
                    min_qty = lot_size_filter.get('minOrderQty', 'unknown')
                    
                    print(f"✅ SPK现货存在!")
                    print(f"   真实basePrecision: {base_precision}")
                    print(f"   真实minOrderQty: {min_qty}")
                    print(f"   系统默认step_size: 0.001")
                    
                    if str(base_precision) != "0.001":
                        print(f"   🚨 步长不匹配！真实={base_precision}, 默认=0.001")
                        print(f"   🚨 这解释了为什么321.995会导致 '170137: Order quantity has too many decimals' 错误！")
                        
                    results["spk_spot"] = {
                        "exists": True,
                        "real_base_precision": base_precision,
                        "real_min_qty": min_qty,
                        "default_step": "0.001",
                        "step_mismatch": str(base_precision) != "0.001"
                    }
                else:
                    print(f"❌ SPK现货不存在")
                    results["spk_spot"] = {"exists": False}
            else:
                print(f"❌ API响应异常")
                results["spk_spot"] = {"exists": False, "error": "API响应异常"}
                
        except Exception as e:
            print(f"❌ SPK现货测试失败: {e}")
            results["spk_spot"] = {"exists": False, "error": str(e)}
            
        # 分析结果
        print(f"\n{'='*60}")
        print("🎯 根本原因分析")
        print(f"{'='*60}")
        
        root_cause_found = False
        
        if results.get("icnt_futures", {}).get("step_mismatch"):
            print(f"🚨 ICNT期货步长不匹配确认！")
            print(f"   真实步长: {results['icnt_futures']['real_qty_step']}")
            print(f"   系统默认: {results['icnt_futures']['default_step']}")
            print(f"   这导致153.307不符合真实精度要求")
            root_cause_found = True
            
        if results.get("spk_spot", {}).get("step_mismatch"):
            print(f"🚨 SPK现货步长不匹配确认！")
            print(f"   真实步长: {results['spk_spot']['real_base_precision']}")
            print(f"   系统默认: {results['spk_spot']['default_step']}")
            print(f"   这导致321.995不符合真实精度要求")
            root_cause_found = True
            
        if root_cause_found:
            print(f"\n✅ 根本原因确认：系统使用的默认步长与Bybit真实API要求不符！")
            print(f"🔧 修复方案：")
            print(f"   1. 修复交易规则预加载器的API调用逻辑")
            print(f"   2. 确保能正确获取真实的Bybit交易规则")
            print(f"   3. 避免回退到不准确的默认值")
        else:
            print(f"⚠️ 未发现明显的步长不匹配，需要进一步调查")
            
        # 保存结果
        results_file = "123/diagnostic_results/simple_bybit_test.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
            
        print(f"\n📊 测试结果已保存到: {results_file}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_bybit_real_rules())
