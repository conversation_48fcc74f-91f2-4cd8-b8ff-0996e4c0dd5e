#!/usr/bin/env python3
"""
追踪事件循环的创建时机
找出是什么导致了事件循环冲突
"""

import os
import sys
import asyncio

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def trace_event_loop_creation():
    print("🔍 追踪事件循环创建时机")
    print("="*60)
    
    # 检查初始状态
    print("📊 初始状态:")
    try:
        loop = asyncio.get_running_loop()
        print(f"   ❌ 已有运行中的事件循环: {loop}")
        print(f"   事件循环关闭状态: {loop.is_closed()}")
    except RuntimeError:
        print("   ✅ 没有运行中的事件循环")
    print()
    
    # 逐步导入模块，检查每步是否创建事件循环
    modules_to_test = [
        "core.trading_rules_preloader",
        "exchanges.bybit_exchange", 
        "exchanges.gate_exchange",
        "core.universal_token_system"
    ]
    
    for module_name in modules_to_test:
        print(f"📦 导入模块: {module_name}")
        
        try:
            # 检查导入前的状态
            try:
                loop_before = asyncio.get_running_loop()
                print(f"   导入前: 有事件循环 {loop_before}")
            except RuntimeError:
                print("   导入前: 无事件循环")
                
            # 导入模块
            __import__(module_name)
            
            # 检查导入后的状态
            try:
                loop_after = asyncio.get_running_loop()
                print(f"   导入后: 有事件循环 {loop_after}")
                print(f"   ⚠️  模块 {module_name} 创建了事件循环!")
            except RuntimeError:
                print("   导入后: 无事件循环")
                
        except Exception as e:
            print(f"   💥 导入失败: {e}")
        print()
    
    # 测试具体的调用
    print("🧪 测试具体调用:")
    print("-" * 40)
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        
        print("1. 获取预加载器实例...")
        try:
            loop_before = asyncio.get_running_loop()
            print(f"   调用前: 有事件循环 {loop_before}")
        except RuntimeError:
            print("   调用前: 无事件循环")
            
        preloader = get_trading_rules_preloader()
        
        try:
            loop_after = asyncio.get_running_loop()
            print(f"   调用后: 有事件循环 {loop_after}")
            print(f"   ⚠️  get_trading_rules_preloader() 创建了事件循环!")
        except RuntimeError:
            print("   调用后: 无事件循环")
        print()
        
        print("2. 调用get_trading_rule方法...")
        try:
            loop_before = asyncio.get_running_loop()
            print(f"   调用前: 有事件循环 {loop_before}")
        except RuntimeError:
            print("   调用前: 无事件循环")
            
        # 这里会触发问题
        result = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
        
        try:
            loop_after = asyncio.get_running_loop()
            print(f"   调用后: 有事件循环 {loop_after}")
        except RuntimeError:
            print("   调用后: 无事件循环")
            
        print(f"   结果: {result}")
        
    except Exception as e:
        print(f"   💥 测试失败: {e}")
        import traceback
        traceback.print_exc()
    print()
    
    # 分析asyncio.run()的使用
    print("🔍 分析asyncio.run()使用:")
    print("-" * 40)
    
    # 模拟问题场景
    print("1. 模拟在事件循环中调用asyncio.run():")
    
    async def test_nested_asyncio_run():
        print("   在异步函数中...")
        try:
            # 这会失败
            result = asyncio.run(asyncio.sleep(0.1))
            print("   ✅ asyncio.run()成功")
        except RuntimeError as e:
            print(f"   ❌ asyncio.run()失败: {e}")
            
    # 创建事件循环来测试
    try:
        asyncio.run(test_nested_asyncio_run())
    except Exception as e:
        print(f"   测试异常: {e}")
    print()
    
    print("🎯 结论:")
    print("-" * 40)
    print("   问题原因:")
    print("   1. 某个模块或调用创建了事件循环")
    print("   2. 在该事件循环中调用asyncio.run()会失败")
    print("   3. 这是Python异步编程的限制")
    print()
    print("   解决方案:")
    print("   1. 使用await代替asyncio.run()")
    print("   2. 或者使用loop.create_task()")
    print("   3. 或者确保在没有事件循环的环境中调用")

if __name__ == "__main__":
    trace_event_loop_creation()
