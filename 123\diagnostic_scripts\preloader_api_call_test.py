#!/usr/bin/env python3
"""
测试交易规则预加载器的API调用
验证为什么API调用成功但仍使用默认规则
"""

import os
import sys
import asyncio
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def test_preloader_api_call():
    print("🚀 测试交易规则预加载器API调用...")
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        from exchanges.bybit_exchange import BybitExchange
        
        preloader = get_trading_rules_preloader()
        
        # 创建Bybit实例
        bybit = BybitExchange(
            api_key="dummy",
            api_secret="dummy", 
            testnet=False
        )
        
        results = {}
        
        # 测试ICNT-USDT期货
        print(f"\n🧪 测试预加载器获取ICNT-USDT期货精度...")
        try:
            precision_info = await preloader._get_precision_from_exchange_api(
                bybit, "ICNT-USDT", "futures"
            )
            
            if precision_info:
                print(f"✅ 预加载器API调用成功!")
                print(f"   步长: {precision_info.get('step_size', 'unknown')}")
                print(f"   来源: {precision_info.get('source', 'unknown')}")
                print(f"   最小数量: {precision_info.get('min_amount', 'unknown')}")
                print(f"   最大数量: {precision_info.get('max_amount', 'unknown')}")
                print(f"   数量精度: {precision_info.get('amount_precision', 'unknown')}")
                
                results["icnt_futures"] = {
                    "success": True,
                    "precision_info": precision_info
                }
                
                # 验证步长是否正确
                expected_step = 1.0  # 从直接API调用得知真实步长是1
                actual_step = precision_info.get('step_size')
                if actual_step == expected_step:
                    print(f"   ✅ 步长正确: {actual_step}")
                else:
                    print(f"   🚨 步长错误: 期望={expected_step}, 实际={actual_step}")
                    
            else:
                print(f"❌ 预加载器返回None")
                results["icnt_futures"] = {
                    "success": False,
                    "error": "返回None"
                }
                
        except Exception as e:
            error_str = str(e)
            print(f"❌ 预加载器API调用异常: {error_str}")
            results["icnt_futures"] = {
                "success": False,
                "error": error_str
            }
            
        # 测试SPK-USDT现货
        print(f"\n🧪 测试预加载器获取SPK-USDT现货精度...")
        try:
            precision_info = await preloader._get_precision_from_exchange_api(
                bybit, "SPK-USDT", "spot"
            )
            
            if precision_info:
                print(f"✅ 预加载器API调用成功!")
                print(f"   步长: {precision_info.get('step_size', 'unknown')}")
                print(f"   来源: {precision_info.get('source', 'unknown')}")
                print(f"   最小数量: {precision_info.get('min_amount', 'unknown')}")
                print(f"   最大数量: {precision_info.get('max_amount', 'unknown')}")
                print(f"   数量精度: {precision_info.get('amount_precision', 'unknown')}")
                
                results["spk_spot"] = {
                    "success": True,
                    "precision_info": precision_info
                }
                
                # 验证步长是否正确
                expected_step = 0.1  # 从直接API调用得知真实步长是0.1
                actual_step = precision_info.get('step_size')
                if actual_step == expected_step:
                    print(f"   ✅ 步长正确: {actual_step}")
                else:
                    print(f"   🚨 步长错误: 期望={expected_step}, 实际={actual_step}")
                    
            else:
                print(f"❌ 预加载器返回None")
                results["spk_spot"] = {
                    "success": False,
                    "error": "返回None"
                }
                
        except Exception as e:
            error_str = str(e)
            print(f"❌ 预加载器API调用异常: {error_str}")
            results["spk_spot"] = {
                "success": False,
                "error": error_str
            }
            
        # 测试完整的交易规则获取流程
        print(f"\n🧪 测试完整交易规则获取流程...")
        
        # 测试ICNT期货规则获取
        try:
            icnt_rule = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
            if icnt_rule:
                print(f"✅ ICNT期货规则获取成功:")
                print(f"   步长: {icnt_rule.qty_step}")
                print(f"   来源: {icnt_rule.source}")
                print(f"   最小数量: {icnt_rule.min_qty}")
                
                results["icnt_futures_rule"] = {
                    "success": True,
                    "qty_step": float(icnt_rule.qty_step),
                    "source": icnt_rule.source,
                    "min_qty": float(icnt_rule.min_qty)
                }
                
                # 检查是否使用了正确的API规则
                if icnt_rule.source == "api" and float(icnt_rule.qty_step) == 1.0:
                    print(f"   ✅ 使用了正确的API规则")
                elif icnt_rule.source == "default":
                    print(f"   🚨 仍在使用默认规则！这是问题所在！")
                else:
                    print(f"   ⚠️ 规则来源异常: {icnt_rule.source}, 步长: {icnt_rule.qty_step}")
            else:
                print(f"❌ ICNT期货规则获取失败")
                results["icnt_futures_rule"] = {"success": False}
                
        except Exception as e:
            print(f"❌ ICNT期货规则获取异常: {e}")
            results["icnt_futures_rule"] = {"success": False, "error": str(e)}
            
        # 测试SPK现货规则获取
        try:
            spk_rule = preloader.get_trading_rule("bybit", "SPK-USDT", "spot")
            if spk_rule:
                print(f"✅ SPK现货规则获取成功:")
                print(f"   步长: {spk_rule.qty_step}")
                print(f"   来源: {spk_rule.source}")
                print(f"   最小数量: {spk_rule.min_qty}")
                
                results["spk_spot_rule"] = {
                    "success": True,
                    "qty_step": float(spk_rule.qty_step),
                    "source": spk_rule.source,
                    "min_qty": float(spk_rule.min_qty)
                }
                
                # 检查是否使用了正确的API规则
                if spk_rule.source == "api" and float(spk_rule.qty_step) == 0.1:
                    print(f"   ✅ 使用了正确的API规则")
                elif spk_rule.source == "default":
                    print(f"   🚨 仍在使用默认规则！这是问题所在！")
                else:
                    print(f"   ⚠️ 规则来源异常: {spk_rule.source}, 步长: {spk_rule.qty_step}")
            else:
                print(f"❌ SPK现货规则获取失败")
                results["spk_spot_rule"] = {"success": False}
                
        except Exception as e:
            print(f"❌ SPK现货规则获取异常: {e}")
            results["spk_spot_rule"] = {"success": False, "error": str(e)}
            
        # 分析结果
        print(f"\n{'='*60}")
        print("🎯 问题分析")
        print(f"{'='*60}")
        
        # 检查API调用是否成功但规则仍为默认
        icnt_api_success = results.get("icnt_futures", {}).get("success", False)
        icnt_rule_default = results.get("icnt_futures_rule", {}).get("source") == "default"
        
        spk_api_success = results.get("spk_spot", {}).get("success", False)
        spk_rule_default = results.get("spk_spot_rule", {}).get("source") == "default"
        
        if icnt_api_success and icnt_rule_default:
            print(f"🚨 ICNT期货：API调用成功但规则仍为默认！")
            print(f"   这说明API调用结果没有被正确保存到缓存中")
            
        if spk_api_success and spk_rule_default:
            print(f"🚨 SPK现货：API调用成功但规则仍为默认！")
            print(f"   这说明API调用结果没有被正确保存到缓存中")
            
        # 保存结果
        results_file = "123/diagnostic_results/preloader_api_call_test.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
            
        print(f"\n📊 测试结果已保存到: {results_file}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_preloader_api_call())
