#!/usr/bin/env python3
"""
简化的交易规则测试
"""

import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_trading_rules():
    print("🚀 测试交易规则获取...")
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        
        preloader = get_trading_rules_preloader()
        
        # 清除缓存
        preloader.trading_rules.clear()
        preloader.unsupported_pairs.clear()
        
        print("🧹 缓存已清除")
        
        # 测试ICNT期货
        print("\n🧪 测试ICNT-USDT期货...")
        icnt_rule = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
        
        if icnt_rule:
            print(f"✅ 成功获取规则:")
            print(f"   步长: {icnt_rule.qty_step}")
            print(f"   来源: {icnt_rule.source}")
            
            if float(icnt_rule.qty_step) == 1.0:
                print(f"   ✅ 步长正确！")
            else:
                print(f"   🚨 步长错误，期望1.0，实际{icnt_rule.qty_step}")
        else:
            print(f"❌ 获取规则失败")
            
        # 测试SPK现货
        print("\n🧪 测试SPK-USDT现货...")
        spk_rule = preloader.get_trading_rule("bybit", "SPK-USDT", "spot")
        
        if spk_rule:
            print(f"✅ 成功获取规则:")
            print(f"   步长: {spk_rule.qty_step}")
            print(f"   来源: {spk_rule.source}")
            
            if float(spk_rule.qty_step) == 0.1:
                print(f"   ✅ 步长正确！")
            else:
                print(f"   🚨 步长错误，期望0.1，实际{spk_rule.qty_step}")
        else:
            print(f"❌ 获取规则失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_trading_rules()
