#!/usr/bin/env python3
"""
Bybit API调用调试脚本
专门调试为什么Bybit API调用失败，导致使用默认规则
"""

import os
import sys
import asyncio
import time
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class BybitAPICallDebug:
    def __init__(self):
        self.results = {
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "api_call_tests": {},
            "root_cause_found": False,
            "fix_recommendations": []
        }
        
    def print_header(self, title: str):
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
        
    async def test_bybit_api_direct_call(self):
        """直接测试Bybit API调用"""
        self.print_header("直接Bybit API调用测试")
        
        try:
            from exchanges.bybit_exchange import BybitExchange
            
            # 创建Bybit实例
            bybit = BybitExchange(
                api_key="dummy",
                api_secret="dummy", 
                testnet=False
            )
            
            # 测试ICNT-USDT期货
            print(f"\n🧪 测试 ICNT-USDT 期货 API调用...")
            try:
                response = await bybit.get_instruments_info("linear", "ICNTUSDT")
                
                if response and "result" in response and "list" in response["result"]:
                    instruments = response["result"]["list"]
                    if instruments:
                        instrument = instruments[0]
                        lot_size_filter = instrument.get("lotSizeFilter", {})
                        
                        print(f"✅ API调用成功!")
                        print(f"   交易对: {instrument.get('symbol', 'unknown')}")
                        print(f"   qtyStep: {lot_size_filter.get('qtyStep', 'unknown')}")
                        print(f"   minOrderQty: {lot_size_filter.get('minOrderQty', 'unknown')}")
                        print(f"   maxOrderQty: {lot_size_filter.get('maxOrderQty', 'unknown')}")
                        
                        self.results["api_call_tests"]["icnt_futures"] = {
                            "success": True,
                            "qty_step": lot_size_filter.get('qtyStep', 'unknown'),
                            "min_order_qty": lot_size_filter.get('minOrderQty', 'unknown'),
                            "raw_data": lot_size_filter
                        }
                    else:
                        print(f"❌ API返回空列表 - 交易对不存在")
                        self.results["api_call_tests"]["icnt_futures"] = {
                            "success": False,
                            "error": "交易对不存在"
                        }
                else:
                    print(f"❌ API响应格式异常")
                    self.results["api_call_tests"]["icnt_futures"] = {
                        "success": False,
                        "error": "API响应格式异常",
                        "response": response
                    }
                    
            except Exception as e:
                error_str = str(e)
                print(f"❌ API调用异常: {error_str}")
                self.results["api_call_tests"]["icnt_futures"] = {
                    "success": False,
                    "error": error_str
                }
                
            # 测试SPK-USDT现货
            print(f"\n🧪 测试 SPK-USDT 现货 API调用...")
            try:
                response = await bybit.get_instruments_info("spot", "SPKUSDT")
                
                if response and "result" in response and "list" in response["result"]:
                    instruments = response["result"]["list"]
                    if instruments:
                        instrument = instruments[0]
                        lot_size_filter = instrument.get("lotSizeFilter", {})
                        
                        print(f"✅ API调用成功!")
                        print(f"   交易对: {instrument.get('symbol', 'unknown')}")
                        print(f"   basePrecision: {lot_size_filter.get('basePrecision', 'unknown')}")
                        print(f"   minOrderQty: {lot_size_filter.get('minOrderQty', 'unknown')}")
                        print(f"   maxOrderQty: {lot_size_filter.get('maxOrderQty', 'unknown')}")
                        
                        self.results["api_call_tests"]["spk_spot"] = {
                            "success": True,
                            "base_precision": lot_size_filter.get('basePrecision', 'unknown'),
                            "min_order_qty": lot_size_filter.get('minOrderQty', 'unknown'),
                            "raw_data": lot_size_filter
                        }
                    else:
                        print(f"❌ API返回空列表 - 交易对不存在")
                        self.results["api_call_tests"]["spk_spot"] = {
                            "success": False,
                            "error": "交易对不存在"
                        }
                else:
                    print(f"❌ API响应格式异常")
                    self.results["api_call_tests"]["spk_spot"] = {
                        "success": False,
                        "error": "API响应格式异常",
                        "response": response
                    }
                    
            except Exception as e:
                error_str = str(e)
                print(f"❌ API调用异常: {error_str}")
                self.results["api_call_tests"]["spk_spot"] = {
                    "success": False,
                    "error": error_str
                }
                
        except Exception as e:
            print(f"❌ Bybit交易所初始化失败: {e}")
            
    async def test_trading_rules_preloader_api_call(self):
        """测试交易规则预加载器的API调用"""
        self.print_header("交易规则预加载器API调用测试")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            from exchanges.bybit_exchange import BybitExchange
            
            preloader = get_trading_rules_preloader()
            
            # 创建临时Bybit实例
            bybit = BybitExchange(
                api_key="dummy",
                api_secret="dummy", 
                testnet=False
            )
            
            # 测试预加载器的API调用方法
            print(f"\n🧪 测试预加载器获取ICNT-USDT期货精度...")
            try:
                precision_info = await preloader._get_precision_from_exchange_api(
                    bybit, "ICNT-USDT", "futures"
                )
                
                if precision_info:
                    print(f"✅ 预加载器API调用成功!")
                    print(f"   步长: {precision_info.get('step_size', 'unknown')}")
                    print(f"   来源: {precision_info.get('source', 'unknown')}")
                    print(f"   最小数量: {precision_info.get('min_amount', 'unknown')}")
                    
                    self.results["api_call_tests"]["preloader_icnt_futures"] = {
                        "success": True,
                        "precision_info": precision_info
                    }
                else:
                    print(f"❌ 预加载器返回None")
                    self.results["api_call_tests"]["preloader_icnt_futures"] = {
                        "success": False,
                        "error": "返回None"
                    }
                    
            except Exception as e:
                error_str = str(e)
                print(f"❌ 预加载器API调用异常: {error_str}")
                self.results["api_call_tests"]["preloader_icnt_futures"] = {
                    "success": False,
                    "error": error_str
                }
                
            # 测试SPK-USDT现货
            print(f"\n🧪 测试预加载器获取SPK-USDT现货精度...")
            try:
                precision_info = await preloader._get_precision_from_exchange_api(
                    bybit, "SPK-USDT", "spot"
                )
                
                if precision_info:
                    print(f"✅ 预加载器API调用成功!")
                    print(f"   步长: {precision_info.get('step_size', 'unknown')}")
                    print(f"   来源: {precision_info.get('source', 'unknown')}")
                    print(f"   最小数量: {precision_info.get('min_amount', 'unknown')}")
                    
                    self.results["api_call_tests"]["preloader_spk_spot"] = {
                        "success": True,
                        "precision_info": precision_info
                    }
                else:
                    print(f"❌ 预加载器返回None")
                    self.results["api_call_tests"]["preloader_spk_spot"] = {
                        "success": False,
                        "error": "返回None"
                    }
                    
            except Exception as e:
                error_str = str(e)
                print(f"❌ 预加载器API调用异常: {error_str}")
                self.results["api_call_tests"]["preloader_spk_spot"] = {
                    "success": False,
                    "error": error_str
                }
                
        except Exception as e:
            print(f"❌ 预加载器测试失败: {e}")
            
    def analyze_root_cause(self):
        """分析根本原因"""
        self.print_header("根本原因分析")
        
        # 检查直接API调用是否成功
        icnt_direct = self.results["api_call_tests"].get("icnt_futures", {})
        spk_direct = self.results["api_call_tests"].get("spk_spot", {})
        
        # 检查预加载器API调用是否成功
        icnt_preloader = self.results["api_call_tests"].get("preloader_icnt_futures", {})
        spk_preloader = self.results["api_call_tests"].get("preloader_spk_spot", {})
        
        print(f"📊 API调用结果对比:")
        print(f"   ICNT期货直接调用: {'成功' if icnt_direct.get('success') else '失败'}")
        print(f"   ICNT期货预加载器: {'成功' if icnt_preloader.get('success') else '失败'}")
        print(f"   SPK现货直接调用: {'成功' if spk_direct.get('success') else '失败'}")
        print(f"   SPK现货预加载器: {'成功' if spk_preloader.get('success') else '失败'}")
        
        # 分析真实步长与默认值的差异
        if icnt_direct.get("success") and icnt_direct.get("qty_step"):
            real_step = icnt_direct.get("qty_step")
            print(f"\n🚨 ICNT期货真实步长: {real_step} (默认: 0.001)")
            if str(real_step) != "0.001":
                print(f"   ⚠️ 真实步长与默认值不符！这是错误的根本原因！")
                self.results["root_cause_found"] = True
                self.results["fix_recommendations"].append({
                    "issue": "ICNT期货步长不匹配",
                    "real_step": real_step,
                    "default_step": "0.001",
                    "fix": f"更新默认规则或修复API调用"
                })
                
        if spk_direct.get("success") and spk_direct.get("base_precision"):
            real_step = spk_direct.get("base_precision")
            print(f"\n🚨 SPK现货真实步长: {real_step} (默认: 0.001)")
            if str(real_step) != "0.001":
                print(f"   ⚠️ 真实步长与默认值不符！这是错误的根本原因！")
                self.results["root_cause_found"] = True
                self.results["fix_recommendations"].append({
                    "issue": "SPK现货步长不匹配",
                    "real_step": real_step,
                    "default_step": "0.001",
                    "fix": f"更新默认规则或修复API调用"
                })
                
    async def run_debug(self):
        """运行完整调试"""
        print("🚀 开始Bybit API调用调试...")
        
        await self.test_bybit_api_direct_call()
        await self.test_trading_rules_preloader_api_call()
        self.analyze_root_cause()
        
        # 保存结果
        results_file = "123/diagnostic_results/bybit_api_call_debug.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
            
        print(f"\n📊 调试结果已保存到: {results_file}")
        
        # 总结
        print(f"\n{'='*60}")
        print("🎯 调试总结")
        print(f"{'='*60}")
        print(f"🔍 API调用测试: {len(self.results['api_call_tests'])}")
        print(f"🚨 根本原因发现: {'是' if self.results['root_cause_found'] else '否'}")
        print(f"🔧 修复建议: {len(self.results['fix_recommendations'])}")
        
        if self.results["root_cause_found"]:
            print("✅ 根本原因已发现，可以开始修复")
            for rec in self.results["fix_recommendations"]:
                print(f"   🚨 {rec['issue']}: 真实={rec['real_step']}, 默认={rec['default_step']}")
        else:
            print("⚠️ 需要进一步调查")

if __name__ == "__main__":
    debug = BybitAPICallDebug()
    asyncio.run(debug.run_debug())
