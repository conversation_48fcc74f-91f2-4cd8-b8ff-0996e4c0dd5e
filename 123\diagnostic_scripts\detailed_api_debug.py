#!/usr/bin/env python3
"""
详细的API调试脚本
逐步跟踪交易规则预加载器的API调用过程
"""

import os
import sys
import asyncio
import json
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 设置详细日志
logging.basicConfig(level=logging.DEBUG)

async def debug_api_call_step_by_step():
    print("🚀 开始详细API调试...")
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        from exchanges.bybit_exchange import BybitExchange
        from core.universal_token_system import get_universal_token_system
        
        preloader = get_trading_rules_preloader()
        token_system = get_universal_token_system()
        
        # 创建Bybit实例
        bybit = BybitExchange(
            api_key="dummy",
            api_secret="dummy", 
            testnet=False
        )
        
        print(f"\n🧪 调试ICNT-USDT期货API调用过程...")
        
        # 步骤1：获取交易所格式的symbol
        exchange_name = "bybit"
        symbol = "ICNT-USDT"
        market_type = "futures"
        
        print(f"   原始symbol: {symbol}")
        
        try:
            exchange_symbol = token_system.get_exchange_symbol_format(symbol, exchange_name, market_type)
            print(f"   转换后symbol: {exchange_symbol}")
        except Exception as e:
            print(f"   ❌ symbol转换失败: {e}")
            return
            
        # 步骤2：检查交易所是否有get_instruments_info方法
        has_method = hasattr(bybit, 'get_instruments_info')
        print(f"   hasattr(bybit, 'get_instruments_info'): {has_method}")
        
        if not has_method:
            print(f"   ❌ Bybit交易所没有get_instruments_info方法")
            return
            
        # 步骤3：确定category
        category = "spot" if market_type == "spot" else "linear"
        print(f"   category: {category}")
        
        # 步骤4：尝试API调用
        print(f"   🔄 调用 bybit.get_instruments_info('{category}', '{exchange_symbol}')...")
        
        try:
            instruments_info = await bybit.get_instruments_info(category, exchange_symbol)
            print(f"   ✅ API调用成功")
            print(f"   响应类型: {type(instruments_info)}")
            print(f"   响应键: {list(instruments_info.keys()) if isinstance(instruments_info, dict) else 'Not a dict'}")
            
            # 步骤5：解析响应格式
            instruments = None
            
            # 格式1：直接包含list
            if instruments_info and "list" in instruments_info:
                instruments = instruments_info["list"]
                print(f"   📋 使用格式1: 直接list格式，找到{len(instruments)}个交易对")
                
            # 格式2：result.list格式
            elif instruments_info and "result" in instruments_info and "list" in instruments_info["result"]:
                instruments = instruments_info["result"]["list"]
                print(f"   📋 使用格式2: result.list格式，找到{len(instruments)}个交易对")
            else:
                print(f"   ❌ 无法识别响应格式")
                print(f"   完整响应: {instruments_info}")
                return
                
            # 步骤6：解析交易对数据
            if instruments:
                instrument = instruments[0]
                print(f"   📊 交易对数据:")
                print(f"      symbol: {instrument.get('symbol', 'unknown')}")
                
                lot_size_filter = instrument.get("lotSizeFilter", {})
                price_filter = instrument.get("priceFilter", {})
                
                print(f"      lotSizeFilter: {lot_size_filter}")
                print(f"      priceFilter: {price_filter}")
                
                # 步骤7：提取步长
                if market_type == "spot":
                    base_precision = lot_size_filter.get("basePrecision", "0.000001")
                    step_size = base_precision
                    print(f"   🎯 现货步长: basePrecision={base_precision}")
                else:
                    step_size = lot_size_filter.get("qtyStep", "0.001")
                    print(f"   🎯 期货步长: qtyStep={step_size}")
                    
                # 步骤8：构建精度信息
                min_order_qty = float(lot_size_filter.get("minOrderQty", "0.001"))
                
                precision_info = {
                    "step_size": float(step_size),
                    "min_amount": min_order_qty,
                    "max_amount": float(lot_size_filter.get("maxOrderQty", "1000000")),
                    "price_precision": preloader._calculate_precision(price_filter.get("tickSize", "0.01")),
                    "amount_precision": preloader._calculate_precision(step_size),
                    "min_notional": float(lot_size_filter.get("minOrderAmt", "5.0")),
                    "source": "api"
                }
                
                print(f"   ✅ 构建的精度信息:")
                for key, value in precision_info.items():
                    print(f"      {key}: {value}")
                    
                # 验证步长是否正确
                expected_step = 1.0  # ICNT期货的真实步长
                actual_step = precision_info["step_size"]
                
                if actual_step == expected_step:
                    print(f"   ✅ 步长正确: {actual_step}")
                else:
                    print(f"   🚨 步长不匹配: 期望={expected_step}, 实际={actual_step}")
                    
            else:
                print(f"   ❌ 没有找到交易对数据")
                
        except Exception as api_error:
            print(f"   ❌ API调用失败: {api_error}")
            print(f"   错误类型: {type(api_error)}")
            import traceback
            traceback.print_exc()
            
        # 现在测试完整的预加载器方法
        print(f"\n🧪 测试完整的预加载器方法...")
        
        try:
            precision_info = await preloader._get_precision_from_exchange_api(
                bybit, symbol, market_type
            )
            
            if precision_info:
                print(f"   ✅ 预加载器方法成功")
                print(f"   返回的精度信息: {precision_info}")
            else:
                print(f"   ❌ 预加载器方法返回None")
                
        except Exception as preloader_error:
            print(f"   ❌ 预加载器方法失败: {preloader_error}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_api_call_step_by_step())
